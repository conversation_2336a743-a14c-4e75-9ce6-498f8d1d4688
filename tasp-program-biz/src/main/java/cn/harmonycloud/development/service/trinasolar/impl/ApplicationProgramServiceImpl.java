package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.development.config.GitSyncConfig;
import cn.harmonycloud.development.config.GitSyncDTO;
import cn.harmonycloud.development.convert.thgn.ApplicationProgramConvert;
import cn.harmonycloud.development.execption.thgn.AppException;
import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.outbound.CodeProjectRepository;
import cn.harmonycloud.development.outbound.api.feign.CodeProjectFeign;
import cn.harmonycloud.development.outbound.api.feign.IntegrationProvider;
import cn.harmonycloud.development.outbound.db.ApplicationPersonnelRepositoryImpl;
import cn.harmonycloud.development.outbound.thgn.ApplicationProgramRepository;
import cn.harmonycloud.development.outbound.thgn.ScaffoldComponentRepository;
import cn.harmonycloud.development.outbound.thgn.ScaffoldTemplateRepository;
import cn.harmonycloud.development.service.trinasolar.GitlabService;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.thgn.GitlabCommitCountDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationCheckUserInAppDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDetailDTO;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationUserDTO;
import cn.harmonycloud.trinasolar.model.entity.*;
import cn.harmonycloud.development.pojo.dto.thgn.*;
import cn.harmonycloud.development.pojo.vo.thgn.app.ApplicationProgramVO;
import cn.harmonycloud.development.service.DevopsApplicationPipelineService;
import cn.harmonycloud.development.service.mapstruct.thgn.ApplicationProgramMapstruct;
import cn.harmonycloud.development.service.trinasolar.ApplicationProgramService;
import cn.harmonycloud.development.service.trinasolar.UserService;
import cn.harmonycloud.development.util.DateTimeUtils;
import cn.harmonycloud.development.util.RequestUtils;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.enums.thgn.ProgramEnum;
import cn.harmonycloud.issue.model.CommonResult;
import cn.harmonycloud.trinasolar.CloudProvider;
import cn.harmonycloud.trinasolar.UpmsGatewayProvider;
import cn.harmonycloud.trinasolar.UpmsProvider;
import cn.harmonycloud.trinasolar.model.*;
import cn.harmonycloud.trinasolar.model.vo.PipelineRespVO;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ApplicationProgramServiceImpl implements ApplicationProgramService {

    public static final String GITLAB = "GITLAB";
    public static final String URL = "${url}";
    public static final String GROUP = "${group}";
    public static final String ADMIN = "admin";
    public static final String DEFAULT_TIME = "1970-01-01 08:00:00";
    public static final Integer DEFAULT_SIZE = 4;
    @Autowired
    UserService userService;

    @Autowired
    private CodeProjectRepository codeProjectRepository;

    @Autowired
    private CodeProjectFeign codeProjectFeign;

    @Autowired
    private ApplicationProgramMapstruct applicationProgramMapstruct;

    @Autowired
    private ApplicationProgramRepository applicationProgramRepository;

    @Autowired
    private ScaffoldTemplateRepository scaffoldTemplateRepository;

    @Autowired
    private ScaffoldComponentRepository scaffoldComponentRepository;

    @Autowired
    private GitSyncConfig gitSyncConfig;

    @Autowired
    private ApplicationProgramConvert applicationProgramConvert;

    @Autowired
    private IntegrationProvider integrationProvider;

    // 新增Repository
    @Autowired
    private ApplicationPersonnelRepositoryImpl applicationPersonnelRepository;

    @Autowired
    private UpmsProvider upmsProvider;

    @Autowired
    private UpmsGatewayProvider upmsGatewayProvider;

    @Autowired
    private DevopsApplicationPipelineService devopsApplicationPipelineService;

    @Autowired
    private CloudProvider cloudProvider;

    @Autowired
    @Qualifier("taskExecutor")
    ThreadPoolTaskExecutor executor;

    @Autowired
    private GitlabService gitlabService;

    @Override
    public void modifyApplicationProgram(ApplicationProgramDTO programDTO) {
        // 获取参数
        String programDescCn = programDTO.getProgramDescCn();
        List<Long> developDirectorId = programDTO.getDevelopDirectorId();
        String currentRunningVersion = programDTO.getCurrentRunningVersion();
        Long applicationId = programDTO.getApplicationId();
        String relateGitUrl = programDTO.getRelateGitUrl();
        Long id = programDTO.getId();
        //应用系统基础信息
        AppSystemBaseDTO data = getAppSystemBaseDTO(applicationId);

        programDTO.setBusinessDomain(data.getBusinessDomain());
        programDTO.setApplicationEnName(data.getEnName());
        String programNameEn = programDTO.getProgramNameEn();
        // 参数非空校验
        if (CollectionUtils.isEmpty(developDirectorId)) {
            throw new IllegalArgumentException("开发负责人列表不能为空");
        }
        if (StrUtil.isBlank(currentRunningVersion)) {
            throw new IllegalArgumentException("当前应用程序版本不能为空");
        }
        if (id == null) {
            throw new IllegalArgumentException("应用程序ID不能为空");
        }
        // 查询并校验记录存在性
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(id);
        if (applicationProgram == null) {
            throw new AppException("未找到ID为" + id + "的应用程序记录");
        }
        String existGitUrl = applicationProgram.getGitlabRepoUrl();
        // 更新实体属性
        applicationProgram.setProgramDescCn(programDescCn);
        applicationProgram.setDevelopDirectorId(developDirectorId);
        applicationProgram.setCurrentRunningVersion(currentRunningVersion);
        // 通过判断已有数据是否有初始化过
        if (StringUtils.isEmpty(existGitUrl)) {
            // 没有初始化 ，判断是否启用 true 表示不启用，修改基础信息就可以了
            if (Boolean.TRUE.equals(programDTO.getGitlabEnabled())) {
                applicationProgramRepository.updateById(applicationProgram);
            } else {
                // 没有初始化 ，开始判断是新建还是关联
                // 新建需要自己定义url
                if (programDTO.getWhetherNewlyBuilt()) {
                    //应用系统基础信息
                    AppSystemBaseDTO appSystemBaseDTO = getAppSystemBaseDTO(applicationId);
                    relateGitUrl = getPushGitUrl(programNameEn, appSystemBaseDTO.getBusinessDomain(),
                            gitSyncConfig.getPushGitUrl(), appSystemBaseDTO.getEnSimpleName());
                }
            }
            BeanUtils.copyProperties(applicationProgram, programDTO);
            executor.execute(() -> initApp(programDTO,applicationId, applicationProgram));
        }
        applicationProgram.setGitlabRepoUrl(relateGitUrl);
        applicationProgramRepository.updateById(applicationProgram);
    }

    @Override
    public ApplicationProgram createApplicationProgram(ApplicationProgramDTO programDTO) {
        Long applicationId = programDTO.getApplicationId();
        checkCreate(applicationId, programDTO, true);
        ApplicationProgram applicationProgram = saveLocal(programDTO);
        executor.execute(() -> initApp(programDTO, applicationId, applicationProgram));
        return applicationProgram;
    }

    private void initApp(ApplicationProgramDTO programDTO, Long applicationId, ApplicationProgram program) {
        String programNameEn = programDTO.getProgramNameEn();
        Long programId = program.getId();
        List<Long> userIds = programDTO.getDevelopDirectorId();
        String scaffoldTemplateId = programDTO.getScaffoldTemplateId();
        //应用系统基础信息
        AppSystemBaseDTO appSystemBaseDTO = getAppSystemBaseDTO(applicationId);
        String enSimpleName = appSystemBaseDTO.getEnSimpleName();

        // 判断是否是新建项目并且启用gitlab
        boolean isNewProject = Boolean.FALSE.equals(programDTO.getGitlabEnabled())
                && Boolean.TRUE.equals(programDTO.getWhetherNewlyBuilt());
        log.warn("isNewProject is {}", isNewProject);
        log.warn("scaffoldTemplateId is {}", scaffoldTemplateId);
        // 模板创建、空白创建都需，使用对应模板
        if (StrUtil.isNotEmpty(scaffoldTemplateId) || isNewProject) {
            // 特殊处理空白创建
            ScaffoldTemplate scaffoldTemplate = getIfNullScaffoldTemplate(programDTO, scaffoldTemplateId);
            String gitRepoUrl = scaffoldTemplate.getGitRepoUrl();
            String packageName = programDTO.getPackageName();
            // 英文名称也是group名称
            GitSyncDTO gitSyncDTO = new GitSyncDTO();
            BeanUtils.copyProperties(gitSyncConfig, gitSyncDTO);
            String pushGitUrl = getPushGitUrl(programNameEn, appSystemBaseDTO.getBusinessDomain(), gitSyncDTO.getPushGitUrl(), enSimpleName);
            gitSyncDTO.setPushGitUrl(pushGitUrl).setPullGitUrl(gitRepoUrl).setProgramNameEn(programNameEn).setPackageName(packageName);
            //查询starter组件
            List<ScaffoldComponent> scaffoldComponents = new ArrayList<>();
            if (programDTO.getScaffoldComponentId() != null) {
                //查询组件并过滤掉默认的组件（默认的组件已经在脚手架里）
                scaffoldComponents = scaffoldComponentRepository.listByIds(programDTO.getScaffoldComponentId())
                        .stream().filter(sc -> !sc.getDepDefault()).collect(Collectors.toList());
            }
            try {
                //同步脚手架代码到master分支
                gitlabService.syncGitCode(gitSyncDTO, true, scaffoldTemplate, CollectionUtils.isEmpty(scaffoldComponents) ? null : scaffoldComponents);
                //创建4个环境的分支
                GitlabService.create4EnvBranch(gitSyncDTO.getPushGitUrl(), gitSyncConfig.getPushPrivateToken(), scaffoldTemplate.getDefaultBranch());
                //更新应用程序初始化状态
                program.setGitlabRepoUrl(gitSyncDTO.getPushGitUrl());
                program.setInitStatus(ProgramEnum.INIT_COMPLETE.getCode());
                applicationProgramRepository.updateById(program);
                //创建devops的流水线
                log.warn("scaffoldTemplate is {},{},{}", scaffoldTemplate, com.alibaba.fastjson.JSONObject.toJSONString(program), pushGitUrl);
                CommonResult<Boolean> result = integrationProvider.createApp(program);
                if (result != null && result.getData()) {
                    // 授权
                    addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
                } else {
                    if (result != null) {
                        log.error("流水线创建失败{}", result.getMsg());
                    }
                    throw new GitException("流水线创建失败，详细日志请检查聚合服务！");
                }
            } catch (Exception e) {
                program.setInitStatus(ProgramEnum.INIT_ERROR.getCode());
                applicationProgramRepository.updateById(program);
                log.error("创建脚手架服务异常", e);
                throw new GitException("创建脚手架服务异常");
            }
        } else {
            // 正常创建不关联
            if (programDTO.getGitlabEnabled()) {
                addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
            } else {
                // 关联gitlab项目
                // 应用程序建完成后，创建gitlab项目
                programDTO.setId(programId);
                String pushGitUrl = programDTO.getRelateGitUrl();
                program.setGitlabRepoUrl(pushGitUrl);
                addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
                program.setInitStatus(ProgramEnum.INIT_COMPLETE.getCode());
                applicationProgramRepository.updateById(program);
                //创建devops的流水线
                CommonResult<Boolean> result = integrationProvider.createApp(program);
                if (result != null && result.getData()) {
                    // 授权
                    addUserGrantToApplication(programId, applicationId, programNameEn, userIds, Boolean.FALSE);
                } else {
                    if (result != null) {
                        log.error("流水线创建失败{}", result.getMsg());
                    }
                    throw new GitException("流水线创建失败，详细日志请检查聚合服务！");
                }
            }
        }
    }

    private ScaffoldTemplate getIfNullScaffoldTemplate(ApplicationProgramDTO applicationProgramDTO,
                                                       String scaffoldTemplateId) {
        Boolean whetherNewlyBuilt = applicationProgramDTO.getWhetherNewlyBuilt();
        if (Boolean.TRUE.equals(whetherNewlyBuilt)) {
            scaffoldTemplateId = "0";
        }
        // 根据脚手架模版初始化代码仓库
        ScaffoldTemplate scaffoldTemplate = scaffoldTemplateRepository.getById(scaffoldTemplateId);
        if (Boolean.TRUE.equals(whetherNewlyBuilt)) {
            // 空白创建，补充技术栈信息
            scaffoldTemplate.setTechStack(applicationProgramDTO.getTechnicalStackTags());
            scaffoldTemplate.setCategory(applicationProgramDTO.getTechnologyStack());
        }
        return scaffoldTemplate;
    }

    /**
     * 仓库地址
     *
     * @param programNameEn      应用程序名称
     * @param businessDomain     业务域
     * @param pushGitUrlTmp      临时地址
     * @param systemEnSimpleName 系统简称
     * @return
     */
    private static String getPushGitUrl(String programNameEn, String businessDomain, String pushGitUrlTmp,
                                        String systemEnSimpleName) {
        String groupUrl = businessDomain + "/" + systemEnSimpleName;
        String pushGitUrl = pushGitUrlTmp.replace(GROUP, groupUrl);
        pushGitUrl = pushGitUrl.replace(URL, programNameEn);
        return pushGitUrl;
    }

    private void checkCreate(Long applicationId, ApplicationProgramDTO programDTO, Boolean checkSysInit) {
        String programNameCn = programDTO.getProgramNameCn();
        String programNameEn = programDTO.getProgramNameEn();
        if (checkSysInit) {
            CommonResult<Boolean> result = integrationProvider.initCheck(applicationId);
            if (!CommonResult.isSuccess(result.getCode()) || !result.getData()) {
                throw new AppException("应用系统未初始化，请稍等！");
            }
        }

        // 判断一个应用系统下数据库是否有同名的
        Optional<ApplicationProgram> cnNameOptional = applicationProgramRepository.lambdaQuery()
                .eq(ApplicationProgram::getApplicationId, applicationId)
                .eq(ApplicationProgram::getProgramNameCn, programNameCn)
                .eq(ApplicationProgram::getDelFlag, SystemConstance.NOT_DELETE)
                .oneOpt();
        if (cnNameOptional.isPresent()) {
            throw new AppException("同一应用系统下，应用程序中文名称不能重复:" + cnNameOptional.get().getProgramNameCn());
        }
        // 判断一个应用系统下数据库是否有同名的
        Optional<ApplicationProgram> enNameOptional = applicationProgramRepository.lambdaQuery()
                .eq(ApplicationProgram::getApplicationId, applicationId)
                .eq(ApplicationProgram::getProgramNameEn, programNameEn)
                .eq(ApplicationProgram::getDelFlag, SystemConstance.NOT_DELETE)
                .oneOpt();
        if (enNameOptional.isPresent()) {
            throw new AppException("同一应用系统下，应用程序英文名称不能重复:" + enNameOptional.get().getProgramNameEn());
        }
    }

    @Override
    public Page<ApplicationProgramVO> listApplicationProgram(Long applicationId, String programNameCn,
                                                             Page<ApplicationProgram> page, String technologyStack) {
        User currentUser = RequestUtils.getCurrentUser();
        Long userId = Long.parseLong(currentUser.getId());
        Boolean admin = userService.isAdmin(userId, applicationId);
        // 由于JSON_CONTAINS导致MyBatis Plus分页插件失效，采用手动分页
        log.info("admin is {}", admin);

        // 先构建基础查询条件（不包含分页）
        LambdaQueryChainWrapper<ApplicationProgram> queryWrapper = applicationProgramRepository.lambdaQuery()
                .like(StrUtil.isNotBlank(programNameCn), ApplicationProgram::getProgramNameCn, programNameCn)
                .eq(ApplicationProgram::getApplicationId, applicationId)
                .eq(StrUtil.isNotBlank(technologyStack), ApplicationProgram::getTechnologyStack, technologyStack);

        if (!admin) {
            List<DevopsApplicationProgramPersonnel> devopsApplicationProgramPersonnels = applicationPersonnelRepository.lambdaQuery()
                    .eq(DevopsApplicationProgramPersonnel::getUserId, userId).list();
            List<Long> programIds = devopsApplicationProgramPersonnels.stream()
                    .map(DevopsApplicationProgramPersonnel::getProgramId).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(programIds)) {
                queryWrapper.and(wrapper ->
                        wrapper.apply("JSON_CONTAINS(develop_director_id, '" + userId + "')")
                                .or().eq(ApplicationProgram::getCreateBy, userId));
            } else {
                queryWrapper.and(wrapper ->
                        wrapper.apply("JSON_CONTAINS(develop_director_id, '" + userId + "')")
                                .or().eq(ApplicationProgram::getCreateBy, userId)
                                .or().in(ApplicationProgram::getId, programIds));
            }
        }

        // 手动实现分页：先查询总数
        long total = queryWrapper.count();
        log.info("查询总记录数: {}", total);

        // 再查询分页数据
        List<ApplicationProgram> records = queryWrapper
                .orderByDesc(ApplicationProgram::getUpdateTime)
                .last("LIMIT " + ((page.getCurrent() - 1) * page.getSize()) + ", " + page.getSize())
                .list();

        // 手动构建分页结果
        page.setTotal(total);
        page.setRecords(records);
        page.setPages((long) Math.ceil((double) total / page.getSize()));

        // 添加分页调试日志
        log.info("分页查询结果 - 当前页: {}, 每页大小: {}, 总记录数: {}, 总页数: {}, 当前页记录数: {}",
                page.getCurrent(), page.getSize(), page.getTotal(), page.getPages(), records.size());

        List<ApplicationProgramVO> voList = applicationProgramConvert.toVOList(records);

        // 使用索引来保持顺序的异步处理
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        String token = RequestUtils.getToken();
        log.info("scm token {}", token);
        for (int i = 0; i < voList.size(); i++) {
            final int index = i;
            final ApplicationProgramVO applicationProgramVO = voList.get(index);
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    Integer projectId = codeProjectFeign
                            .getIdByPathV2(applicationProgramVO.getGitlabRepoUrl(), 1L, token).getData();
                    applicationProgramVO.setGitlabId(String.valueOf(projectId));
                } catch (Exception e) {
                    log.error("Failed to get projectId for gitlabRepoUrl: {}", applicationProgramVO.getGitlabRepoUrl(),
                            e);
                }
            });
            futures.add(future);
        }

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 直接使用原有的voList，保持分页顺序
        List<ApplicationProgramVO> recordsDealed = voList;
        Page<ApplicationProgramVO> voPage = new Page<>(
                page.getCurrent(),
                page.getSize(),
                page.getTotal());
        List<Long> createByIds = records.stream().map(ApplicationProgram::getCreateBy).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(createByIds)) {
            List<UserVO> userByIds = userService.getUserByIds(createByIds);
            Map<Long, String> userMap = userByIds.stream()
                    .collect(Collectors.toMap(
                            UserVO::getId,
                            UserVO::getUserRealname));
            recordsDealed.forEach(e -> {
                e.setCreatedBy(userMap.get(e.getCreateBy()));
            });
        }
        voPage.setRecords(recordsDealed);
        voPage.setPages(page.getPages());

        // 添加返回结果调试日志
        log.info("返回分页结果 - 当前页: {}, 每页大小: {}, 总记录数: {}, 总页数: {}, 返回记录数: {}",
                voPage.getCurrent(), voPage.getSize(), voPage.getTotal(), voPage.getPages(), recordsDealed.size());

        return voPage;
    }

    @Override
    public ApplicationCheckUserInAppDTO checkUserInApp(Long applicationId, Long userId) {
        ApplicationCheckUserInAppDTO applicationCheckUserInAppDTO = new ApplicationCheckUserInAppDTO();
        applicationCheckUserInAppDTO.setIsSuccess(Boolean.TRUE);
        List<ApplicationProgram> applicationPrograms = applicationProgramRepository.lambdaQuery()
                .eq(ApplicationProgram::getApplicationId, applicationId).list();
        if (CollectionUtils.isEmpty(applicationPrograms)) {
            return applicationCheckUserInAppDTO;
        }
        List<Long> existDevelopersProgramIds = applicationPrograms.stream().filter(e -> {
            if (!CollectionUtils.isEmpty(e.getDevelopDirectorId()) && e.getDevelopDirectorId().contains(userId)) {
                return true;
            } else {
                return false;
            }
        }).map(ApplicationProgram::getId).collect(Collectors.toList());
        // 查询应用程序下所有创建者
        List<Long> createBy = applicationPrograms.stream().map(ApplicationProgram::getCreateBy)
                .collect(Collectors.toList());
        // 不是开发责任人并且不是开发人员
        if (!existDevelopersProgramIds.contains(userId) && !createBy.contains(userId)) {
            return applicationCheckUserInAppDTO;
        }
        // 找出创建人在的应用程序ID
        List<Long> existCreateByProgramIds = applicationPrograms.stream().filter(e -> e.getCreateBy().equals(userId))
                .map(ApplicationProgram::getId).collect(Collectors.toList());
        // 开发负责人所在应用程序ID
        List<Long> existAllProgramIds = ListUtils.union(existCreateByProgramIds, existDevelopersProgramIds);
        List<ApplicationProgram> existApplicationProgram = applicationPrograms.stream()
                .filter(e -> existAllProgramIds.contains(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existApplicationProgram)) {
            return applicationCheckUserInAppDTO;
        }
        StringBuilder message = new StringBuilder("删除失败，请在以下应用程序中移除该人员:[");
        String msg = existApplicationProgram.stream().map(ApplicationProgram::getProgramNameCn)
                .collect(Collectors.joining(SystemConstance.COMMA));
        applicationCheckUserInAppDTO.setIsSuccess(Boolean.FALSE).setMessage(message.append(msg).append("]").toString());
        return applicationCheckUserInAppDTO;
    }

    @Override
    public List<String> listTechStack(ApplicationProgramDTO applicationProgramDTO) {
        Long systemId = applicationProgramDTO.getSystemId();
        List<ApplicationProgram> list = applicationProgramRepository.lambdaQuery()
                .eq(Objects.nonNull(systemId), ApplicationProgram::getSystemId, systemId).list();
        return list.stream().map(ApplicationProgram::getTechnicalStackTags).distinct().collect(Collectors.toList());
    }

    @Override
    public ApplicationProgramDetailDTO applicationProgramDetail(Long programId) {
        ApplicationProgramDetailDTO applicationProgramDetailDTO = new ApplicationProgramDetailDTO();
        ApplicationProgram applicationProgram = applicationProgramRepository.lambdaQuery()
                .eq(ApplicationProgram::getId, programId).one();
        List<Long> userIds = applicationProgram.getDevelopDirectorId();
        BeanUtils.copyProperties(applicationProgram, applicationProgramDetailDTO);
        // 回显用户
        if (!CollectionUtils.isEmpty(userIds)) {
            R<List<UserVO>> result = upmsGatewayProvider.getByIds(userIds);
            if (!result.isSuccess()) {
                throw new AppException("用户查询异常");
            }
            List<User> entries = new ArrayList<>();
            for (UserVO userVO : result.getData()) {
                User user = new User();
                user.setId(String.valueOf(userVO.getId()));
                user.setUserRealname(userVO.getUserRealname());
                entries.add(user);
            }
            applicationProgramDetailDTO.setDevelopDirectorId(entries);
        }
        Long createBy = applicationProgram.getCreateBy();
        Long updateBy = applicationProgram.getUpdateBy();
        Map<Long, String> userMap = userService.getUserByIds(List.of(createBy, updateBy)).stream()
                .collect(Collectors.toMap(UserVO::getId, UserVO::getUserRealname));
        applicationProgramDetailDTO.setCreatedBy(userMap.get(createBy)).setUpdatedBy(userMap.get(updateBy));
        return applicationProgramDetailDTO;
    }

    @Override
    public Boolean addUserGrantToApplication(Long programId, Long projectId, String appName, List<Long> userIds,
                                             Boolean isDeveloper) {
        List<DevopsApplicationProgramPersonnel> devops = applicationPersonnelRepository.listByProgramId(programId);
        List<Long> grantUserIds = devops.stream().map(DevopsApplicationProgramPersonnel::getUserId)
                .collect(Collectors.toList());
        List<Long> newUserIds = userIds.stream()
                .filter(id -> !grantUserIds.contains(id))
                .collect(Collectors.toList());
        if (newUserIds.isEmpty()) {
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "没有新用户需要添加");
        }
        // 校验用户是否存在
        R<List<UserVO>> result = upmsGatewayProvider.getByIds(userIds);
        if (!result.isSuccess() || CollectionUtils.isEmpty(result.getData())) {
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "获取对应用户数据异常！");
        }
        List<UserVO> users = result.getData();
        List<String> userCodes = users.stream().map(UserVO::getUserCode).collect(Collectors.toList());
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        String gitlabRepoUrl = applicationProgram.getGitlabRepoUrl();
        // 判断是否是开发人员
        if (isDeveloper) {
            saveGrantUser(programId, users, applicationProgram);
        }
        executor.execute(() -> {
            // 判断是否有git地址，判断是否启用
            if (StringUtils.isNotBlank(gitlabRepoUrl)) {
                // 添加用户权限
                addUsersToProject(programId, users);
                // 给用户添加流水线权限
                CommonResult<Boolean> grant = integrationProvider.grant(projectId, appName, userCodes);
                if (grant.isError()) {
                    log.error("应用程序流水线添加用户错误！{}", grant.getMsg());
                    throw new AppException("应用程序流水线添加用户错误！");
                }
            }
        });
        return true;
    }

    public void addUsersToProject(Long programId, List<UserVO> users) {
        List<String> userNames = users.stream().map(UserVO::getUsername).filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)) {
            log.info("用户权限统一集成账户未查询到");
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "用户权限统一集成账户未查询到!");
        }
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        if (applicationProgram == null || StringUtils.isEmpty(applicationProgram.getGitlabRepoUrl())) {
            throw new AppException("应用程序不存在或对应的git地址不存在，请检查！");
        }
        GitlabService.addUsersToProject(applicationProgram.getGitlabRepoUrl(), gitSyncConfig.getPushPrivateToken(),
                userNames);
    }

    public void removeUsersToProject(Long programId, List<UserVO> users) {
        List<String> userNames = users.stream().map(UserVO::getUsername).filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)) {
            log.info("用户权限统一集成账户未查询到");
            throw new AppException(ExceptionCode.INNER_EXCEPTION, "用户权限统一集成账户未查询到!");
        }
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        if (applicationProgram == null || StringUtils.isEmpty(applicationProgram.getGitlabRepoUrl())) {
            throw new AppException("应用程序不存在或对应的git地址不存在，请检查！");
        }
        GitlabService.removeUsersFromProject(applicationProgram.getGitlabRepoUrl(), gitSyncConfig.getPushPrivateToken(),
                userNames);
    }

    private void saveGrantUser(Long programId, List<UserVO> users, ApplicationProgram applicationProgram) {
        for (UserVO userVO : users) {
            Long id = userVO.getId();
            // 否则创建新记录
            DevopsApplicationProgramPersonnel personnel = new DevopsApplicationProgramPersonnel();
            personnel.setProgramId(programId);
            personnel.setUserId(id);
            personnel.setRoleId("developer");
            personnel.setPersonnelType("developer");
            personnel.setCreateTime(LocalDateTime.now());
            personnel.setUpdateTime(LocalDateTime.now());
            personnel.setDelFlag(SystemConstance.NOT_DELETE);
            applicationPersonnelRepository.save(personnel);
        }

    }

    @Override
    public Boolean removeUserToApplication(Long programId, Long projectId, String appName, List<Long> userIds) {
        R<List<UserVO>> result = upmsGatewayProvider.getByIds(userIds);
        if (!result.isSuccess()) {
            throw new AppException("用户查询异常");
        }
        List<UserVO> users = result.getData();
        List<String> userCodes = users.stream().map(UserVO::getUserCode).collect(Collectors.toList());
        ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
        executor.execute(() -> {
            String scaffoldTemplateId = applicationProgram.getScaffoldTemplateId();
            if (StringUtils.isNotBlank(scaffoldTemplateId)) {
                // 给用户删除流水线权限
                integrationProvider.revoke(projectId, appName, userCodes);
                // 移除用户权限
                removeUsersToProject(programId, users);
            }
        });
        return applicationPersonnelRepository.update(
                new LambdaUpdateWrapper<DevopsApplicationProgramPersonnel>()
                        .set(DevopsApplicationProgramPersonnel::getDelFlag, SystemConstance.IS_DELETE)
                        .in(DevopsApplicationProgramPersonnel::getUserId, userIds)
                        .eq(DevopsApplicationProgramPersonnel::getProgramId, programId));
    }

    @Override
    public void deleteApp(Long id) {
        LambdaQueryWrapper<ApplicationProgram> query = new LambdaQueryWrapper<>();
        query.eq(ApplicationProgram::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(ApplicationProgram::getId, id);
        ApplicationProgram program = applicationProgramRepository.getOne(query);
        if (program == null) {
            return;
        }
        // 应用程序关联表删除用户
        applicationPersonnelRepository.update(
                new LambdaUpdateWrapper<DevopsApplicationProgramPersonnel>()
                        .set(DevopsApplicationProgramPersonnel::getDelFlag, SystemConstance.IS_DELETE)
                        .eq(DevopsApplicationProgramPersonnel::getProgramId, id));
        // 调用删除应用程序逻辑
        applicationProgramRepository.update(
                new LambdaUpdateWrapper<ApplicationProgram>()
                        .set(ApplicationProgram::getDelFlag, SystemConstance.IS_DELETE)
                        .eq(ApplicationProgram::getId, id));
        // 删除流水线
        CommonResult<Boolean> booleanCommonResult = integrationProvider.batchDeleteByProgramId(id);
        if (!booleanCommonResult.isSuccess()) {
            log.error("流水线删除失败:{}", booleanCommonResult.getMsg());
            // throw new AppException("流水线删除失败");
        }
    }

    public ApplicationProgram saveLocal(ApplicationProgramDTO request) {
        LocalDateTime now = LocalDateTime.now();
        ApplicationProgram data = applicationProgramMapstruct.toApplicationProgram(request);
        data.setInitStatus(ProgramEnum.INIT_WAITING.getCode());
        User currentUser = RequestUtils.getCurrentUser();
        data.setCreateBy(Long.parseLong(currentUser.getId()));
        data.setCreateTime(now);
        data.setUpdateBy(Long.parseLong(currentUser.getId()));
        data.setUpdateTime(now);
        data.setDevelopDirectorId(request.getDevelopDirectorId());
        applicationProgramRepository.save(data);
        return data;
    }

    @Override
    public List<ApplicationUserDTO> listPersonnelByProgramId(Long programId) {
        List<DevopsApplicationProgramPersonnel> programPersonals = applicationPersonnelRepository.lambdaQuery()
                .eq(DevopsApplicationProgramPersonnel::getProgramId, programId)
                .eq(DevopsApplicationProgramPersonnel::getDelFlag, 0)
                .list();
        if (CollectionUtils.isEmpty(programPersonals)) {
            return null;
        }
        List<Long> userIds = programPersonals.stream().map(DevopsApplicationProgramPersonnel::getUserId)
                .collect(Collectors.toList());
        R<List<UserVO>> result = upmsGatewayProvider.getByIds(userIds);
        if (!result.isSuccess()) {
            throw new AppException("用户查询异常！");
        }
        List<UserVO> users = result.getData();
        if (CollectionUtils.isEmpty(users)) {
            return null;
        }
        return applicationProgramConvert.toApplicationUserDTOs(users);
    }

    @Override
    public PageResult<PipelineRespVO> getPipelines(Long programId) {
        CommonResult<PageResult<PipelineRespVO>> result = integrationProvider.getPipelines(programId);
        if (!result.isSuccess()) {
            throw new AppException("获取流水线异常，请检查聚合聚合服务！");
        }
        PageResult<PipelineRespVO> data = result.getData();
        if (data == null || CollectionUtils.isEmpty(data.getList())) {
            log.info("流水线数据查询为空！");
            return new PageResult<>();
        }
        List<PipelineRespVO> pipelineRespList = data.getList();
        List<DevopsApplicationPipeline> list = devopsApplicationPipelineService.lambdaQuery()
                .eq(DevopsApplicationPipeline::getProgramId, programId).list();
        // 如果库里没有，就存入库，这里主要是防止并发场景
        if (CollectionUtils.isEmpty(list)) {
            ApplicationProgram applicationProgram = applicationProgramRepository.getById(programId);
            List<DevopsApplicationPipeline> applicationPipelineList = new ArrayList<>();
            pipelineRespList.forEach(e -> {
                DevopsApplicationPipeline devopsApplicationPipeline = new DevopsApplicationPipeline();
                LocalDateTime createTime = DateTimeUtils.stringToLocalDateTime(e.getCreateTime());
                devopsApplicationPipeline.setApplicationId(applicationProgram.getApplicationId())
                        .setProgramId(programId)
                        .setPipelineId(e.getPipelineId()).setPipelineName(e.getPipelineName())
                        .setEnvironment(e.getEnvName())
                        .setRunningStatus(e.getLatestBuildStatus()).setBuildUser(e.getBuildUser())
                        .setPipelineCreateTime(createTime);
                // 防止流水线重复入库
                if (applicationPipelineList.stream()
                        .noneMatch(p -> p.getPipelineId().equals(devopsApplicationPipeline.getPipelineId()))) {
                    applicationPipelineList.add(devopsApplicationPipeline);
                }
            });
            applicationPipelineList.forEach(pipeline -> {
                // Check if pipeline exists and update or save accordingly
                LambdaUpdateWrapper<DevopsApplicationPipeline> updateWrapper = new LambdaUpdateWrapper<DevopsApplicationPipeline>()
                        .eq(DevopsApplicationPipeline::getPipelineId, pipeline.getPipelineId());

                DevopsApplicationPipeline existingPipeline = devopsApplicationPipelineService.getOne(updateWrapper);
                if (existingPipeline != null) {
                    pipeline.setId(existingPipeline.getId());
                }
                devopsApplicationPipelineService.saveOrUpdate(pipeline);
            });
            pipelineRespList.forEach(p -> {
                if (DEFAULT_TIME.equals(p.getLatestBuildStartTime())
                        || DEFAULT_TIME.equals(p.getLatestBuildEndTime())) {
                    p.setLatestBuildEndTime(null);
                    p.setLatestBuildStartTime(null);
                }
            });
            return data;
        }
        List<String> pipelineIds = pipelineRespList.stream().map(PipelineRespVO::getPipelineId)
                .collect(Collectors.toList());
        List<DevopsApplicationPipeline> existPipelines = devopsApplicationPipelineService.lambdaQuery()
                .in(DevopsApplicationPipeline::getPipelineId, pipelineIds).list();
        pipelineRespList.forEach(p -> {
            existPipelines.forEach(e -> {
                if (p.getPipelineId().equals(e.getPipelineId())) {
                    // 如果是admin用户执行 就默认以库的数据为准，因为都是用admin的账号执行的
                    if (!ADMIN.equals(e.getBuildUser())) {
                        p.setBuildUser(e.getBuildUser());
                    }
                }
            });
            if (DEFAULT_TIME.equals(p.getLatestBuildStartTime()) || DEFAULT_TIME.equals(p.getLatestBuildEndTime())) {
                p.setLatestBuildEndTime(null);
                p.setLatestBuildStartTime(null);
            }
        });
        return data;
    }

    @Override
    public JSONObject deploy(Long projectId, String pipelineId) {
        String userRealName = userService.getUserRealName();
        CommonResult<JSONObject> result = integrationProvider.deploy(projectId, pipelineId);
        if (result.isError()) {
            log.error("{}", result.getMsg());
            throw new AppException("流水线执行异常：" + result.getMsg());
        }
        devopsApplicationPipelineService.lambdaUpdate().set(DevopsApplicationPipeline::getBuildUser, userRealName)
                .eq(DevopsApplicationPipeline::getPipelineId, pipelineId).update();
        return result.getData();
    }

    @Override
    public Integer count() {
        return Math.toIntExact(applicationProgramRepository.count());
    }

    @Override
    public void deleteAllPrograms(Long id) {
        if (id == null) {
            return;
        }
        applicationProgramRepository.lambdaUpdate()
                .eq(ApplicationProgram::getApplicationId, id)
                .remove();
    }

    @Override
    public List<String> getGitList(Long id) {
        String subgroupPath = getSubgroupPath(id);
        String pushGitUrlTmp = gitSyncConfig.getPushGitUrl();
        String gitlabDomain = getGitlabDomain(pushGitUrlTmp);
        return GitlabService.getSubgroupProjectsGitList(subgroupPath,
                gitSyncConfig.getPushPrivateToken(), gitlabDomain);
    }

    private String getSubgroupPath(Long id) {
        //应用系统信息
        AppSystemBaseDTO data = getAppSystemBaseDTO(id);
        // 业务域
        String businessDomainValue = data.getBusinessDomain();
        String enName = data.getEnName();
        if (StringUtils.isEmpty(businessDomainValue) || StringUtils.isEmpty(enName)) {
            log.error("未获取到id:{}应用系统！", id);
            throw new AppException("未获取到id:" + id + "应用系统");
        }
        String subgroupPath = businessDomainValue + "/" + enName;
        return subgroupPath;
    }

    private static String getGitlabDomain(String pushGitUrlTmp) {
        Pattern pattern = Pattern.compile("(https://[^/]+)");
        Matcher matcher = pattern.matcher(pushGitUrlTmp);
        String gitlabDomain = matcher.find() ? matcher.group(1) : null;
        return gitlabDomain;
    }

    @Override
    public TechStackRankRespDTO getTechStackRank(String startTime, String endTime) {
        // 解析日期参数（只解析一次）
        LocalDateTime startDate = null;
        LocalDateTime endDate = null;
        if (StrUtil.isNotBlank(startTime)) {
            try {
                startDate = DateUtil.parseLocalDateTime(startTime, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                log.error("日期格式解析错误: {}", startTime, e);
                throw new AppException("开始时间格式错误，应为yyyy-MM-dd HH:mm:ss");
            }
        }
        if (StrUtil.isNotBlank(endTime)) {
            try {
                endDate = DateUtil.parseLocalDateTime(endTime, "yyyy-MM-dd HH:mm:ss");
            } catch (Exception e) {
                log.error("日期格式解析错误: {}", endTime, e);
                throw new AppException("结束时间格式错误，应为yyyy-MM-dd HH:mm:ss");
            }
        }

        // 1. 统计总数量（直接通过count查询，不加载所有数据）
        QueryWrapper<ApplicationProgram> totalQuery = new QueryWrapper<>();
        if (startDate != null) {
            totalQuery.ge("create_time", startDate);
        }
        if (endDate != null) {
            totalQuery.le("create_time", endDate);
        }
        Long totalCount = applicationProgramRepository.count(totalQuery);

        // 2. 统计前端数量（直接通过count查询，不加载所有数据）
        QueryWrapper<ApplicationProgram> frontendQuery = new QueryWrapper<>();
        frontendQuery.eq("technology_stack", TechStackTypeEnum.FRONTEND.getName());
        if (startDate != null) {
            frontendQuery.ge("create_time", startDate);
        }
        if (endDate != null) {
            frontendQuery.le("create_time", endDate);
        }
        Long frontendCount = applicationProgramRepository.count(frontendQuery);
        Long backendCount = totalCount - frontendCount;

        // 3. 统计技术栈使用情况
        QueryWrapper<ApplicationProgram> techStackQuery = new QueryWrapper<>();
        techStackQuery.select("technical_stack_tags", "COUNT(*) AS count")
                .isNotNull("technical_stack_tags")
                .groupBy("technical_stack_tags")
                .last("ORDER BY count DESC");
        if (startDate != null) {
            techStackQuery.ge("create_time", startDate);
        }
        if (endDate != null) {
            techStackQuery.le("create_time", endDate);
        }

        List<Map<String, Object>> techStackResult = applicationProgramRepository.getBaseMapper().selectMaps(techStackQuery);
        List<TechStackRankRespDTO.TechStackInfo> techStackInfoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(techStackResult)) {
            // 1. 处理前9个技术栈
            int limit = Math.min(9, techStackResult.size());
            for (int i = 0; i < limit; i++) {
                Map<String, Object> map = techStackResult.get(i);
                TechStackRankRespDTO.TechStackInfo info = new TechStackRankRespDTO.TechStackInfo();
                String techStack = (String) map.get("technical_stack_tags");
                Long stackCount = Long.parseLong(map.get("count").toString());
                info.setTechnicalStackTags(techStack);
                info.setCount(stackCount);
                techStackInfoList.add(info);
            }

            // 2. 处理其他技术栈汇总
            if (techStackResult.size() > 9) {
                TechStackRankRespDTO.TechStackInfo otherInfo = new TechStackRankRespDTO.TechStackInfo();
                otherInfo.setTechnicalStackTags("其他");

                long otherCount = 0;
                for (int i = 9; i < techStackResult.size(); i++) {
                    Map<String, Object> map = techStackResult.get(i);
                    otherCount += Long.parseLong(map.get("count").toString());
                }

                otherInfo.setCount(otherCount);
                techStackInfoList.add(otherInfo);
            }
        }

        // 4. 构建响应对象
        TechStackRankRespDTO respDTO = new TechStackRankRespDTO();
        respDTO.setApplicationTotalCount(totalCount);
        respDTO.setFrontendApplicationCount(frontendCount);
        respDTO.setBackendApplicationCount(backendCount);
        respDTO.setTechStackUsageList(techStackInfoList);

        return respDTO;
    }

    @Override
    public List<GitlabCommitInfoDTO> getUserGitCommit(String startTime, String endTime, Long projectId) {
        String pushGitUrlTmp = gitSyncConfig.getPushGitUrl();
        String pullPrivateToken = gitSyncConfig.getPullPrivateToken();
        String gitlabDomain = getGitlabDomain(pushGitUrlTmp);
        String subgroupPath = getSubgroupPath(projectId);
        List<String> gitlabIds = GitlabService.getSubgroupProjectsIdsList(subgroupPath, pullPrivateToken, gitlabDomain);
        List<GitlabCommitCountDTO> userCommitCountsByTimeAndGitlabIds = GitlabService.getUserCommitCountsByTimeAndGitlabIds(gitlabDomain, pullPrivateToken, gitlabIds, startTime, endTime);
        List<ApplicationProgram> applicationPrograms = applicationProgramRepository.lambdaQuery().eq(ApplicationProgram::getApplicationId, projectId).isNotNull(ApplicationProgram::getGitlabRepoUrl).list();
        List<GitlabCommitInfoDTO> gitlabCommitInfos = new ArrayList<>();
        List<String> gitUrls = userCommitCountsByTimeAndGitlabIds.stream().map(GitlabCommitCountDTO::getGitUrl).toList();
        List<ApplicationProgram> list = applicationPrograms.stream().filter(e -> gitUrls.contains(e.getGitlabRepoUrl())).toList();
        list.forEach(a -> {
            Optional<GitlabCommitCountDTO> first = userCommitCountsByTimeAndGitlabIds.stream().filter(e -> e.getGitUrl().equals(a.getGitlabRepoUrl())).findFirst();
            if (first.isPresent()) {
                GitlabCommitInfoDTO info = new GitlabCommitInfoDTO();
                GitlabCommitCountDTO commitCountDTO = first.get();
                BeanUtils.copyProperties(commitCountDTO, info);
                info.setProgramNameCn(a.getProgramNameCn());
                info.setProgramNameEn(a.getProgramNameEn());
                gitlabCommitInfos.add(info);
            }
        });
        return gitlabCommitInfos;
    }

    @Override
    public List<ProjectCommitInfoDTO> getUserSysCommit(String startTime, String endTime, String username, Long userId) {
        List<ProjectCommitInfoDTO> projectCommitInfoDTOS = Collections.synchronizedList(new ArrayList<>());
        if (StringUtils.isEmpty(username) || userId == null) {
            return projectCommitInfoDTOS;
        }
        // 获取用户真实名称
        R<List<UserVO>> userIds = upmsGatewayProvider.getByIds(List.of(userId));
        if (!userIds.isSuccess()) {
            throw new AppException("未获取到用户信息，用户id:" + userId);
        }
        UserVO userVO = userIds.getData().get(0);
        String userRealname = userVO.getUserRealname();

        // 获取用户所在应用系统
        String token = RequestUtils.getToken();
        R<List<ProjectInfoDTO>> appsInfo = upmsGatewayProvider.getAppsInfo(token, userId);
        if (!appsInfo.isSuccess()) {
            throw new AppException("获取到用户所在应用系统异常，用户id:" + userId);
        }
        List<ProjectInfoDTO> data = appsInfo.getData();
        Map<String, String> projectMap = data.stream().collect(Collectors.toMap(ProjectInfoDTO::getAppId, ProjectInfoDTO::getCnName));
        List<String> ids = data.stream().map(ProjectInfoDTO::getAppId).toList();
        if (CollectionUtils.isEmpty(ids)) {
            return projectCommitInfoDTOS;
        }
        // 获取指定applicationId的所有应用程序
        List<ApplicationProgram> projectList = applicationProgramRepository.lambdaQuery().in(ApplicationProgram::getApplicationId, ids).list();
        // 按applicationId分组，且确保gitlabRepoUrl不为空且不同
        Map<Long, List<ApplicationProgram>> applicationMap = projectList.stream()
                .filter(app -> app.getGitlabRepoUrl() != null && !app.getGitlabRepoUrl().trim().isEmpty())
                .collect(Collectors.groupingBy(
                        ApplicationProgram::getApplicationId,
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ApplicationProgram::getGitlabRepoUrl))),
                                ArrayList::new
                        )
                ));

        // 这里可以使用applicationMap进行后续操作
        log.info("按applicationId分组且gitlabRepoUrl不同的结果: {}", applicationMap);
        String pushGitUrlTmp = gitSyncConfig.getPushGitUrl();
        String pullPrivateToken = gitSyncConfig.getPullPrivateToken();
        String gitlabDomain = getGitlabDomain(pushGitUrlTmp);
        // 使用线程池异步处理每个applicationId的统计任务
        ExecutorService executorService = Executors.newFixedThreadPool(Math.min(data.size(), 10));
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        data.forEach(e -> {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                List<ApplicationProgram> applicationPrograms = applicationMap.get(Long.parseLong(e.getAppId()));
                ProjectCommitInfoDTO userCommitStatsByGitUrlsAndUsername = new ProjectCommitInfoDTO();
                userCommitStatsByGitUrlsAndUsername.setUsername(userRealname);
                userCommitStatsByGitUrlsAndUsername.setProjectName(projectMap.get(e.getAppId()));
                if (CollectionUtils.isEmpty(applicationPrograms)) {
                    userCommitStatsByGitUrlsAndUsername.setCommitCount(0);
                } else {
                    List<String> urls = applicationPrograms.stream().map(ApplicationProgram::getGitlabRepoUrl).distinct().toList();
                    userCommitStatsByGitUrlsAndUsername = GitlabService.getUserTotalCommitStatsByGitUrls(gitlabDomain, pullPrivateToken, urls, username, startTime, endTime);
                }
                projectCommitInfoDTOS.add(userCommitStatsByGitUrlsAndUsername);
            }, executorService);
            futures.add(future);
        });
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        executorService.shutdown(); // 关闭线程池
        // 根据提交次数降序排序
        projectCommitInfoDTOS.sort((dto1, dto2) -> Integer.compare(dto2.getCommitCount(), dto1.getCommitCount()));
        return projectCommitInfoDTOS;
    }

    @Override
    public ScaffoldTemplateUseInfoRespDTO getScaffoldTemplateInfoRank(String startTime, String endTime) {
        QueryWrapper<ApplicationProgram> queryRank = new QueryWrapper<>();
        queryRank.select("scaffold_template_id", "COUNT(*) AS count")
                .isNotNull("scaffold_template_id")
                .groupBy("scaffold_template_id")
                .last("ORDER BY count DESC LIMIT 9");

        // 查询总数量（用于计算其他类别）
        QueryWrapper<ApplicationProgram> queryTotal = new QueryWrapper<>();
        queryTotal.isNotNull("scaffold_template_id");
        if (StrUtil.isNotBlank(startTime)) {
            try {
                LocalDateTime startDate = DateUtil.parseLocalDateTime(startTime, "yyyy-MM-dd HH:mm:ss");
                queryTotal.ge("create_time", startDate);
            } catch (Exception e) {
                log.error("日期格式解析错误: {}", startTime, e);
                throw new AppException("开始时间格式错误，应为yyyy-MM-dd HH:mm:ss");
            }
        }
        if (StrUtil.isNotBlank(endTime)) {
            try {
                LocalDateTime endDate = DateUtil.parseLocalDateTime(endTime, "yyyy-MM-dd HH:mm:ss");
                queryTotal.le("create_time", endDate);
            } catch (Exception e) {
                log.error("日期格式解析错误: {}", endTime, e);
                throw new AppException("结束时间格式错误，应为yyyy-MM-dd HH:mm:ss");
            }
        }
        Long totalCount = applicationProgramRepository.getBaseMapper().selectCount(queryTotal);
        // 添加时间范围过滤到queryRank
        if (StrUtil.isNotBlank(startTime)) {
            try {
                LocalDateTime startDate = DateUtil.parseLocalDateTime(startTime, "yyyy-MM-dd HH:mm:ss");
                queryRank.ge("create_time", startDate);
            } catch (Exception e) {
                log.error("日期格式解析错误: {}", startTime, e);
                throw new AppException("开始时间格式错误，应为yyyy-MM-dd HH:mm:ss");
            }
        }
        if (StrUtil.isNotBlank(endTime)) {
            try {
                LocalDateTime endDate = DateUtil.parseLocalDateTime(endTime, "yyyy-MM-dd HH:mm:ss");
                queryRank.le("create_time", endDate);
            } catch (Exception e) {
                log.error("日期格式解析错误: {}", endTime, e);
                throw new AppException("结束时间格式错误，应为yyyy-MM-dd HH:mm:ss");
            }
        }

        // 执行查询并处理结果
        List<Map<String, Object>> resultList = applicationProgramRepository.getBaseMapper().selectMaps(queryRank);
        ScaffoldTemplateUseInfoRespDTO respDTO = new ScaffoldTemplateUseInfoRespDTO();
        List<ScaffoldTemplateInfo> templateUseInfoList = new ArrayList<>();
        long top9Total = 0;

        if (!CollectionUtils.isEmpty(resultList)) {
            // 提取所有模板ID
            List<Integer> templateIds = resultList.stream()
                    .map(map -> Integer.parseInt(map.get("scaffold_template_id").toString()))
                    .collect(Collectors.toList());

            // 查询模板信息
            List<ScaffoldTemplate> templates = scaffoldTemplateRepository.listByIds(templateIds);
            Map<Long, String> templateNameMap = templates.stream()
                    .collect(Collectors.toMap(ScaffoldTemplate::getId, ScaffoldTemplate::getName));

            // 映射前9个结果
            for (Map<String, Object> map : resultList) {
                ScaffoldTemplateInfo infoDTO = new ScaffoldTemplateInfo();
                Long templateId = Long.parseLong(map.get("scaffold_template_id").toString());
                long currentCount = Long.parseLong(map.get("count").toString());
                infoDTO.setScaffoldTemplateName(templateNameMap.getOrDefault(templateId, templateId.toString()));
                infoDTO.setCount(currentCount);
                templateUseInfoList.add(infoDTO);
                top9Total += currentCount;
            }

            // 计算其他类别数量
            long otherCount = totalCount - top9Total;
            if (otherCount > 0) {
                ScaffoldTemplateInfo otherInfo = new ScaffoldTemplateInfo();
                otherInfo.setScaffoldTemplateName("其他");
                otherInfo.setCount(otherCount);
                templateUseInfoList.add(otherInfo);
            }
        }

        respDTO.setCount(totalCount);
        respDTO.setApps(templateUseInfoList);
        return respDTO;
    }

    public Boolean createCheck(ApplicationProgramDTO applicationProgramDTO) {
        checkCreate(applicationProgramDTO.getApplicationId(), applicationProgramDTO, true);
        return true;
    }

    public Boolean createBatch(List<ApplicationProgramDTO> applicationProgramDTOList) {
        for (ApplicationProgramDTO applicationProgramDTO : applicationProgramDTOList) {
            Long applicationId = applicationProgramDTO.getApplicationId();
            checkCreate(applicationId, applicationProgramDTO, false);
            saveLocal(applicationProgramDTO);
        }
        return true;
    }

    private AppSystemBaseDTO getAppSystemBaseDTO(Long applicationId) {
        R<AppSystemBaseDTO> appSystemBaseR = cloudProvider.getAppSystemBaseById(applicationId);
        if (appSystemBaseR == null || R.fail().isSuccess()) {
            log.error("获取应用系统详情失败！{}", R.fail().getMessage());
            throw new AppException("获取应用系统详情失败！");
        }
        return appSystemBaseR.getData();
    }
}
