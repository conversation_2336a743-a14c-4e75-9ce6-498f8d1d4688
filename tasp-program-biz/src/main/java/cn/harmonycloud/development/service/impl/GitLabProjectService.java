package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.*;
import cn.harmonycloud.development.outbound.api.dto.coderepo.*;
import cn.harmonycloud.development.outbound.api.dto.scm.GitProjectDto;
import cn.harmonycloud.development.outbound.api.dto.scm.ProjectRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.UpdateProjectRequest;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.scm.RelateProjectRequest;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.SubSystemComponent;
import cn.harmonycloud.development.pojo.entity.SystemDict;
import cn.harmonycloud.development.pojo.vo.system.SubSystemInfoDto;
import cn.harmonycloud.development.service.SubSystemMemberService;
import cn.harmonycloud.development.service.SystemComponentService;

import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.enums.SubSystemComponentEnum;
import cn.harmonycloud.exception.SystemException;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.BeanInitializationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GitLabProjectService {

    private final SubsystemComponentRepository subsystemComponentRepository;
    private final CodeProjectRepository codeProjectRepository;
    private final CodeBranchRepository codeBranchRepository;
    private final CodeMergeRepository codeMergeRepository;
    private final IamRepository iamRepository;
    private final @Lazy SubSystemMemberService subSystemMemberService;
    private final SystemComponentService systemComponentService;
    private final SubsystemRepository subsystemRepository;
    private final FeatureBranchRepository featureBranchRepository;
    private final SystemDictRepository systemDictRepository;

    private String baseBranch;

    @Autowired
    public GitLabProjectService(
            SubsystemComponentRepository subsystemComponentRepository,
            CodeProjectRepository codeProjectRepository,
            CodeBranchRepository codeBranchRepository,
            CodeMergeRepository codeMergeRepository,
            IamRepository iamRepository,
            @Lazy SubSystemMemberService subSystemMemberService,
            SystemComponentService systemComponentService,
            SubsystemRepository subsystemRepository,
            FeatureBranchRepository featureBranchRepository,
            SystemDictRepository systemDictRepository) {
        this.subsystemComponentRepository = subsystemComponentRepository;
        this.codeProjectRepository = codeProjectRepository;
        this.codeBranchRepository = codeBranchRepository;
        this.codeMergeRepository = codeMergeRepository;
        this.iamRepository = iamRepository;
        this.subSystemMemberService = subSystemMemberService;
        this.systemComponentService = systemComponentService;
        this.subsystemRepository = subsystemRepository;
        this.featureBranchRepository = featureBranchRepository;
        this.systemDictRepository = systemDictRepository;
        initBaseBranch();
    }

    private void initBaseBranch() {
        List<SystemDict> systemDict = systemDictRepository.getByParams(SystemConstance.SystemDictSubject.BASE_BRANCH);
        if (CollectionUtils.isEmpty(systemDict)) {
            throw new BeanInitializationException("初始化数据错误：错误的基础分支数据");
        }
        baseBranch = systemDict.get(0).getDictCode();
    }

    private static final String GITLAB = SubSystemComponentEnum.GITLAB.getComponent();

    public GitProjectDto gitlabInfo(Long subSystemId) {
        List<SubSystemComponent> list = subsystemComponentRepository.listByParam(subSystemId, GITLAB);
        if (list == null || list.isEmpty()) {
            throw new SystemException(ExceptionCode.PROMPT, "代码仓库未关联，无法跳转");
        }
        return codeProjectRepository.getProject(Integer.parseInt(list.get(0).getComponentKey()));
    }

    public GitProjectDto updateRelation(String oldId, String newId) {
        List<SubSystemComponent> gitlab = subsystemComponentRepository.listByParam(GITLAB, oldId);
        if(CollectionUtils.isEmpty(gitlab)){
            throw new SystemException(ExceptionCode.PROMPT, "子系统没有关联代码库，更新失败");
        }
        SubSystemComponent newComponent = gitlab.get(0);
        newComponent.setComponentKey(newId);
        subsystemComponentRepository.updateById(newComponent);
        return codeProjectRepository.getProject(Integer.parseInt(newId));
    }

    public void deleteRelation(String gitlabId) {
        List<SubSystemComponent> components = subsystemComponentRepository.listByParam(GITLAB, gitlabId);
        subsystemComponentRepository.removeByParams(GITLAB, gitlabId);
        if (CollectionUtils.isEmpty(components)){
            return;
        }
        List<Long> subsystemIds = components.stream().map(c -> c.getSubSystemId()).collect(Collectors.toList());
        featureBranchRepository.removeByParams(subsystemIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long createProject(Long subSystemId, ProjectRequest request, boolean initCodeScan, boolean initMember) {
        DevopsSubSystem devopsSubSystem = subsystemRepository.getById(subSystemId);
        String groupId = systemComponentService.getComponentKeyBySystemId(devopsSubSystem.getSystemId());
        request.setGroupId(groupId);
        request.setName(devopsSubSystem.getFullNameCn());
        request.setPath(devopsSubSystem.getSubCode());
        request.setInitializeWithReadme(true);
        request.setDescription(devopsSubSystem.getSubDescCn());
        Long gitlabId = codeProjectRepository.createProject(request).getId();
        SubSystemComponent subSystemComponent = new SubSystemComponent();
        subSystemComponent.setSystemId(devopsSubSystem.getSystemId());
        subSystemComponent.setComponent("GITLAB");
        subSystemComponent.setComponentKey(gitlabId.toString());
        subSystemComponent.setSubSystemId(subSystemId);
        subSystemComponent.setKeyType("id");
        subSystemComponent.setCreateBy(iamRepository.getCurrentUser().getId());
        subSystemComponent.setCreateTime(LocalDateTime.now());
        subsystemComponentRepository.save(subSystemComponent);
        if(initMember){
            subSystemMemberService.initGitlabMember(subSystemId, gitlabId);
        }
        if(initCodeScan){
            codeProjectRepository.initCodeScan(gitlabId.intValue(), devopsSubSystem.getTechnology());
        }
        return gitlabId;
    }

    @Transactional(rollbackFor = Exception.class)
    public GitProjectDto relateProject(RelateProjectRequest request, boolean initCodeScan, boolean initMember) {
        List<SubSystemComponent> subSystemComponents = subsystemComponentRepository.listByParam(request.getSubSystemId(), GITLAB);
        if(CollectionUtils.isNotEmpty(subSystemComponents)){
            throw new SystemException(ExceptionCode.PROMPT, "子系统代码库已存在，请勿重复操作");
        }
        List<SubSystemComponent> codeComponents = subsystemComponentRepository.listByParam(GITLAB, request.getGitlabId());
        if(CollectionUtils.isNotEmpty(codeComponents)){
            throw new SystemException(ExceptionCode.PROMPT, "代码仓库已被其他子系统使用");
        }
        Long subSystemId = request.getSubSystemId();
        DevopsSubSystem devopsSubSystem = subsystemRepository.getById(subSystemId);
        SubSystemComponent subSystemComponent = new SubSystemComponent();
        subSystemComponent.setSubSystemId(subSystemId);
        subSystemComponent.setComponent("GITLAB");
        subSystemComponent.setComponentKey(request.getGitlabId());
        subSystemComponent.setKeyType("id");
        subSystemComponent.setSystemId(devopsSubSystem.getSystemId());
        subSystemComponent.setCreateBy(iamRepository.getCurrentUser().getId());
        subSystemComponent.setCreateTime(LocalDateTime.now());

        String groupId = systemComponentService.getComponentKeyBySystemId(devopsSubSystem.getSystemId());
        Integer gitlabId = Integer.parseInt(request.getGitlabId());
        codeProjectRepository.registry(Integer.parseInt(groupId), gitlabId);
        subsystemComponentRepository.save(subSystemComponent);
        if(initMember){
            subSystemMemberService.initGitlabMember(subSystemId, gitlabId.longValue());
        }
        if(initCodeScan){
            codeProjectRepository.initCodeScan(gitlabId, devopsSubSystem.getTechnology());
        }
        return codeProjectRepository.getProject(gitlabId);
    }

    public Integer getScmIdBySystemId(Long subSystemId) {
        List<SubSystemComponent> subSystemComponents = subsystemComponentRepository.listByParam(subSystemId, SubSystemComponentEnum.GITLAB.getComponent());
        if (CollectionUtils.isEmpty(subSystemComponents)) {
            throw new SystemException(ExceptionCode.DATA_IS_NOT_FOUND, "请先关联或创建代码仓库");
        }
        return Integer.parseInt(subSystemComponents.get(0).getComponentKey());
    }

    public Integer getCodeRepoIdBySystemId(Long subSystemId) {
        List<SubSystemComponent> subSystemComponents = subsystemComponentRepository.listByParam(subSystemId, SubSystemComponentEnum.GITLAB.getComponent());
        if (CollectionUtils.isEmpty(subSystemComponents)) {
            return null;
        }
        return Integer.parseInt(subSystemComponents.get(0).getComponentKey());
    }

    public Map<Long, Integer> mapCodeRepoIdBySystemId(List<Long> subSystemIds) {
        if (CollectionUtils.isEmpty(subSystemIds)) {
            return new HashMap<>();
        }
        List<SubSystemComponent> subSystemComponents = subsystemComponentRepository.listByParam(subSystemIds, SubSystemComponentEnum.GITLAB.getComponent());
        if (CollectionUtils.isNotEmpty(subSystemComponents)) {
            return subSystemComponents.stream().collect(Collectors.toMap(SubSystemComponent::getSubSystemId, subSystemComponent -> Integer.parseInt(subSystemComponent.getComponentKey())));
        }
        return new HashMap<>();
    }

    public SubSystemInfoDto getSubSystemInfo(SubSystemInfoDto subSystemInfo) {
        return subSystemInfo;
    }

    public GitProjectDto getProject(Long subSystemId, boolean throwException) {
        Integer scmIdBySystemId = null;
        if (throwException) {
            scmIdBySystemId = getScmIdBySystemId(subSystemId);
        } else {
            scmIdBySystemId = getCodeRepoIdBySystemId(subSystemId);
        }
        if (scmIdBySystemId == null) {
            return null;
        }
        return codeProjectRepository.getProject(scmIdBySystemId);
    }

    public List<BranchDto> branches(Long subSystemId) {
        Integer scmIdBySystemId = getCodeRepoIdBySystemId(subSystemId);
        if (scmIdBySystemId == null) {
            return new ArrayList<>();
        }
        List<BranchDto> branch = codeBranchRepository.getBranch(scmIdBySystemId, "");
        if (CollectionUtils.isEmpty(branch)) {
            return new ArrayList<>();
        }
        for (int i = 0; i < branch.size(); i++) {
            BranchDto branchDto = branch.get(i);
            if (StringUtils.equals(branchDto.getName(), this.baseBranch)) {
                Collections.swap(branch, i, 0);
            }
        }
        return branch;
    }

    public Integer merCount(Long subsystemId, String startTime, String endTime) {
        List<SubSystemComponent> subSystemComponents = subsystemComponentRepository.listByParam(subsystemId, SubSystemComponentEnum.GITLAB.getComponent());
        if (CollectionUtils.isEmpty(subSystemComponents)) {
            return 0;
        }
        Integer scmIdBySystemId = Integer.parseInt(subSystemComponents.get(0).getComponentKey());
        return codeMergeRepository.mergeCount(scmIdBySystemId, startTime, endTime);
    }

    public BranchStageDto branchStage(Long subsystemId, String branch) {
        Integer gitlabId = this.getScmIdBySystemId(subsystemId);
        ArrayList<String> branches = Lists.newArrayList(branch);
        Map<String, BranchStageDto> stringBranchStageDtoMap = codeBranchRepository.listBranchStage(gitlabId, branches, this.baseBranch);
        BranchStageDto branchStageDto = stringBranchStageDtoMap.get(branch);
        if (branchStageDto != null) {
            branchStageDto.setBaseBranch(this.baseBranch);
        }
        return branchStageDto;
    }

    public List<TagDto> tagList(Long subSystemId, String name) {
        Integer gitlabId = this.getCodeRepoIdBySystemId(subSystemId);
        if (gitlabId == null) {
            return new ArrayList<>();
        }
        List<RefDto> result = codeBranchRepository.listTag(gitlabId, name);
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result.stream().map(ref -> ref.getTag()).collect(Collectors.toList());
    }

    public void updateBySubsystem(DevopsSubSystem devopsSubSystem) {
        Integer gitlabId = this.getCodeRepoIdBySystemId(devopsSubSystem.getId());
        if (gitlabId == null) {
            return;
        }
        UpdateProjectRequest request = new UpdateProjectRequest();
        request.setGitlabId(gitlabId.toString());
        request.setName(devopsSubSystem.getFullNameCn());
        request.setDescription(devopsSubSystem.getSubDescCn());
        codeProjectRepository.updateProject(request);
    }

    public List<String> listBranchAndTag(Long subSystemId) {
        Integer gitlabId = this.getCodeRepoIdBySystemId(subSystemId);
        if (gitlabId == null) {
            return new ArrayList<>();
        }
        return codeBranchRepository.listBranchAndTag(gitlabId);
    }

    public void clearMergeBranch(Integer codeRepoId, Integer clearTemporaryBranchTime) {
        List<TempBranchVO> tempBranchVOS = codeBranchRepository.listTempBranch(codeRepoId);
        if (CollectionUtils.isEmpty(tempBranchVOS)) {
            return;
        }
        for (TempBranchVO tempBranchVO : tempBranchVOS) {
            if (tempBranchVO.getCreateTime().plusDays(clearTemporaryBranchTime).isBefore(LocalDateTime.now())) {
                codeBranchRepository.removeBranch(codeRepoId, tempBranchVO.getName());
            }
        }
    }

    public void initCodeScan(Long subsystemId, String technology) {
        Integer codeRepoId = this.getCodeRepoIdBySystemId(subsystemId);
        if (codeRepoId == null) {
            return;
        }
        codeProjectRepository.initCodeScan(codeRepoId, technology);
    }
} 