package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.ConfigRepository;
import cn.harmonycloud.development.outbound.api.dto.config.ConfigCreateDTO;
import cn.harmonycloud.development.outbound.api.dto.config.ConfigEnvCreateDTO;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.CodeConfigFeign;
import cn.harmonycloud.enums.ExceptionCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/10 4:56 下午
 **/
@Service
public class ConfigRepositoryImpl implements ConfigRepository, ApiRepository {

    @Autowired
    private CodeConfigFeign codeConfigFeign;

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_SCM_FAIL;
    }

    @Override
    public Long createConfig(ConfigCreateDTO configCreateDTO) {
        return feignExecute(() -> codeConfigFeign.createConfig(configCreateDTO));
    }

    @Override
    public void createEnv(ConfigEnvCreateDTO configEnvCreateDTO) {
        feignExecute(() -> codeConfigFeign.createEnv(configEnvCreateDTO));
    }
}
