package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.DevopsStageEnvFeatureRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsStageEnvFeatureMapper;
import cn.harmonycloud.development.pojo.entity.DevopsStageEnvFeature;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class DevopsStageEnvFeatureRepositoryImpl extends BaseRepositoryImpl<DevopsStageEnvFeatureMapper,DevopsStageEnvFeature> implements DevopsStageEnvFeatureRepository {


    @Override
    public List<Long> selectFeatureList(Long stageEnvId, Long versionId) {
        LambdaQueryWrapper<DevopsStageEnvFeature> query = new LambdaQueryWrapper<>();
        query.eq(DevopsStageEnvFeature::getStageEnvId, stageEnvId);
        query.eq(versionId != null, DevopsStageEnvFeature::getVersionId, versionId);
        query.isNull(versionId == null, DevopsStageEnvFeature::getVersionId);
        List<DevopsStageEnvFeature> list = Optional.ofNullable(list(query)).orElse(new ArrayList<>());
        return list.stream().map(ef -> ef.getFeatureId()).collect(Collectors.toList());
    }

    @Override
    public void deleteByParam(Long stageEnvId, Long versionId, List<Long> featureIds) {
        if(stageEnvId == null && CollectionUtils.isEmpty(featureIds)){
            return ;
        }
        LambdaUpdateWrapper<DevopsStageEnvFeature> update = new LambdaUpdateWrapper<>();
        update.eq(DevopsStageEnvFeature::getStageEnvId, stageEnvId);
        update.eq(versionId != null, DevopsStageEnvFeature::getVersionId, versionId);
        update.isNull(versionId == null, DevopsStageEnvFeature::getVersionId);
        update.in(DevopsStageEnvFeature::getFeatureId, featureIds);
        this.remove(update);
    }

    @Override
    public void add(Long stageEnvId, Long versionId, List<Long> featureIds) {
        if(stageEnvId == null && CollectionUtils.isEmpty(featureIds)){
            return ;
        }
        List<DevopsStageEnvFeature> collect = featureIds.stream().map(id -> {
            DevopsStageEnvFeature ef = new DevopsStageEnvFeature();
            ef.setStageEnvId(stageEnvId);
            ef.setVersionId(versionId);
            ef.setFeatureId(id);
            return ef;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }


}
