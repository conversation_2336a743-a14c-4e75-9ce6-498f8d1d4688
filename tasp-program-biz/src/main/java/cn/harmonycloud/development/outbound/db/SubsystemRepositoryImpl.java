package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.development.outbound.SubsystemRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsSubSystemMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.subsystem.SubsystemGeneralQuery;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pojo.subsystem.SubsystemDto;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/19 9:28 上午
 **/
@Service
public class SubsystemRepositoryImpl extends BaseRepositoryImpl<DevopsSubSystemMapper, DevopsSubSystem> implements SubsystemRepository {

    @Override
    public List<DevopsSubSystem> listByParams(List<Long> ids, List<Long> systemIds) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.in(CollectionUtils.isNotEmpty(ids), DevopsSubSystem::getId, ids);
        query.in(CollectionUtils.isNotEmpty(systemIds), DevopsSubSystem::getSystemId, systemIds);
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public List<DevopsSubSystem> listByParams(Long systemId, List<Long> ids, String fullNameCn) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(DevopsSubSystem::getSystemId, systemId);
        query.in(DevopsSubSystem::getId, ids);
        query.and(StringUtils.isNotEmpty(fullNameCn), wrapper->wrapper.like(DevopsSubSystem::getFullNameCn, fullNameCn).or().like(DevopsSubSystem::getSubCode, fullNameCn));
        query.orderByDesc(DevopsSubSystem::getCreateTime);
        return this.list(query);
    }

    @Override
    public List<DevopsSubSystem> listByParams(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(DevopsSubSystem::getId, ids);
        return this.list(query);
    }

    @Override
    public List<DevopsSubSystem> listBySystemId(Long systemId) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(DevopsSubSystem::getSystemId, systemId);
        return this.list(query);
    }

    @Override
    public DevopsSubSystem getByParams(String subSystemCode) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(DevopsSubSystem::getSubCode, subSystemCode);
        query.last("limit 1");
        return getOne(query);
    }

    @Override
    public List<DevopsSubSystem> listByParams(String subSystemName, boolean isLike) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        if (isLike){
            query.like(DevopsSubSystem::getFullNameCn, subSystemName);
        }else {
            query.eq(DevopsSubSystem::getFullNameCn, subSystemName);
        }
        return this.list(query);
    }

    @Override
    public List<DevopsSubSystem> listByCode(Long systemId, String subCode) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(DevopsSubSystem::getSystemId, systemId);
        query.eq(DevopsSubSystem::getSubCode, subCode);
        return this.list(query);
    }

    @Override
    public List<DevopsSubSystem> listByParams(Long systemId, String fullNameCn) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(DevopsSubSystem::getSystemId, systemId);
        query.eq(DevopsSubSystem::getFullNameCn, fullNameCn);
        return this.list(query);
    }

    @Override
    public boolean removeLogic(Long id, User currentUser) {
        LambdaUpdateWrapper<DevopsSubSystem> update = new LambdaUpdateWrapper<>();
        update.eq(DevopsSubSystem::getId, id);
        update.set(DevopsSubSystem::getDelFlag, SystemConstance.IS_DELETE);
        update.set(DevopsSubSystem::getCreateBy, currentUser.getId());
        update.set(DevopsSubSystem::getCreateTime, LocalDateTime.now());
        return this.update(update);
    }

    @Override
    public List<DevopsSubSystem> listByParams(SubsystemGeneralQuery queryParam) {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        if(StringUtils.isNotEmpty(queryParam.getFullNameCn()) || CollectionUtils.isNotEmpty(queryParam.getVersionSubIds())){
            if(queryParam.getFullNameCn() == null){
                queryParam.setFullNameCn("");
            }
            query.and(wrapper -> wrapper.like(DevopsSubSystem::getFullNameCn, queryParam.getFullNameCn()).or().in(DevopsSubSystem::getId, queryParam.getVersionSubIds()));
        }
        query.eq(queryParam.getSystemId() != null, DevopsSubSystem::getSystemId, queryParam.getSystemId());
        query.in(CollectionUtils.isNotEmpty(queryParam.getIds()), DevopsSubSystem::getId, queryParam.getIds());
        query.eq(queryParam.getDirectorId()!= null, DevopsSubSystem::getTechDirectorId, queryParam.getDirectorId());
        query.in(CollectionUtils.isNotEmpty(queryParam.getSystemIds()), DevopsSubSystem::getSystemId, queryParam.getSystemIds());
        return this.list(query);
    }

    @Override
    public Map<Long, DevopsSubSystem> mapByIds(List<Long> subIds) {
        if(CollectionUtils.isEmpty(subIds)){
            return new HashMap<>();
        }
        List<DevopsSubSystem> devopsSubSystems = listByIds(subIds);
        return devopsSubSystems.stream().collect(Collectors.toMap(DevopsSubSystem::getId, sub ->sub));
    }

    @Override
    public List<DevopsSubSystem> listAll() {
        LambdaQueryWrapper<DevopsSubSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public boolean batchRemoveLogic(List<Long> ids, User currentUser) {
        LambdaUpdateWrapper<DevopsSubSystem> update = new LambdaUpdateWrapper<>();
        update.in(DevopsSubSystem::getId, ids);
        update.eq(DevopsSubSystem::getDelFlag, SystemConstance.NOT_DELETE);
        update.set(DevopsSubSystem::getDelFlag, SystemConstance.IS_DELETE);
        update.set(DevopsSubSystem::getUpdateBy, currentUser.getId());
        update.set(DevopsSubSystem::getUpdateTime, LocalDateTime.now());
        return this.update(update);
    }
}
