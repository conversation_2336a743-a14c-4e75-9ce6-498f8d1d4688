package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.FeatureRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsFeatureMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.DevopsFeature;
import cn.harmonycloud.development.pojo.entity.DevopsStage;
import cn.harmonycloud.development.pojo.vo.feature.FeatureListQuery;
import cn.harmonycloud.development.pojo.vo.feature.FeaturePageQuery;
import cn.harmonycloud.enums.FeatureStatusEnum;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import net.sf.jsqlparser.statement.select.UnionOp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/20 9:29 上午
 **/
@Service
public class FeatureRepositoryImpl extends BaseRepositoryImpl<DevopsFeatureMapper, DevopsFeature> implements FeatureRepository {

    final String BOTTOM_SQL_TEMPLATE = "order by (case when feature_status in(%s) then 99 else 0 end),create_time desc";

    @Override
    public Page<DevopsFeature> page(FeaturePageQuery request) {
        //如果id为空,返回page
        if (request.getIds() != null && request.getIds().size() == 0){
            return new Page<>(request.getPageNo(), request.getPageSize());
        }
        //根据条件查询
        LambdaQueryWrapper<DevopsFeature> query = new LambdaQueryWrapper<>();
        //未删除、子系统名称、特性类型、名称、创建者、负责人与requset相同
        query.eq(DevopsFeature::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(request.getSubSystemId() != null, DevopsFeature::getSubSystemId, request.getSubSystemId());
        query.eq(request.getSystemId() != null, DevopsFeature::getSystemId, request.getSystemId());
        query.eq(request.getFeatureType() != null, DevopsFeature::getFeatureType, request.getFeatureType());
        query.eq(request.getProjectId() != null, DevopsFeature::getProjectId, request.getProjectId());
        query.and(StringUtils.isNotEmpty(request.getSearch()), wrapper -> wrapper.like(DevopsFeature::getFeatureNumber, request.getSearch()).or().like(DevopsFeature::getFeatureCode, request.getSearch()));
        query.like(StringUtils.isNotEmpty(request.getFeatureName()), DevopsFeature::getFeatureName, request.getFeatureName());
        query.eq(request.getFeatureStatus() != null, DevopsFeature::getFeatureStatus, request.getFeatureStatus());
        query.in(CollectionUtils.isNotEmpty(request.getFeatureStatusList()), DevopsFeature::getFeatureStatus, request.getFeatureStatusList());
        query.eq(request.getCreateBy() != null, DevopsFeature::getCreateBy, request.getCreateBy());
        query.eq(request.getDirector() != null, DevopsFeature::getDirector, request.getDirector());
        //id在request包含的id内,在过滤的id外
        query.in(CollectionUtils.isNotEmpty(request.getIds()), DevopsFeature::getId, request.getIds());
        query.notIn(CollectionUtils.isNotEmpty(request.getIdsFilter()), DevopsFeature::getId, request.getIdsFilter());
        query.notIn(CollectionUtils.isNotEmpty(request.getFeatureStatusFilter()), DevopsFeature::getFeatureStatus, request.getFeatureStatusFilter());
        //根据创建时间倒序排序
        if(CollectionUtils.isNotEmpty(request.getBottomFeatureStatus())){
            List<Integer> bottomFeatureStatus = request.getBottomFeatureStatus();
            String last = String.format(BOTTOM_SQL_TEMPLATE, Joiner.on(",").join(bottomFeatureStatus));
            query.last(last);
        }else {
            query.orderByDesc(DevopsFeature::getCreateTime);
        }
        Page<DevopsFeature> page = new Page<>(request.getPageNo(), request.getPageSize());
        //得到查询结果并分页返回
        return this.page(page, query);
    }

    @Override
    public List<DevopsFeature> list(FeatureListQuery request) {
        LambdaQueryWrapper<DevopsFeature> query = new LambdaQueryWrapper<>();
        query.eq(DevopsFeature::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(request.getSubSystemId() != null,DevopsFeature::getSubSystemId, request.getSubSystemId());
        query.eq(request.getProjectId() != null, DevopsFeature::getProjectId, request.getProjectId());
        query.like(StringUtils.isNotEmpty(request.getFeatureName()), DevopsFeature::getFeatureName, request.getFeatureName());
        query.eq(request.getFeatureStatus() != null, DevopsFeature::getFeatureStatus, request.getFeatureStatus());
        query.eq(request.getDirector() != null, DevopsFeature::getDirector, request.getDirector());
        query.in(CollectionUtils.isNotEmpty(request.getIds()), DevopsFeature::getId, request.getIds());
        query.ge(StringUtils.isNotEmpty(request.getStartTime()), DevopsFeature::getCreateTime, request.getStartTime());
        query.notIn(CollectionUtils.isNotEmpty(request.getFeatureStatusFilter()), DevopsFeature::getFeatureStatus, request.getFeatureStatusFilter());
        query.le(StringUtils.isNotEmpty(request.getEndTime()), DevopsFeature::getCreateTime, request.getEndTime());
        query.eq(StringUtils.isNotEmpty(request.getBranchDesc()), DevopsFeature::getBranchDesc, request.getBranchDesc());
        query.orderByDesc(DevopsFeature::getCreateTime);
        return this.list(query);
    }

    @Override
    public List<DevopsFeature> listBySystemId(Long systemId) {
        LambdaQueryWrapper<DevopsFeature> query = new LambdaQueryWrapper<>();
        query.eq(DevopsFeature::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(DevopsFeature::getSystemId, systemId);
        return this.list(query);
    }

    @Override
    public List<DevopsFeature> listBySubSystemIds(List<Long> subIds) {
        LambdaQueryWrapper<DevopsFeature> query = new LambdaQueryWrapper<>();
        query.eq(DevopsFeature::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(DevopsFeature::getSubSystemId, subIds);
        return this.list(query);
    }

    @Override
    public int maxFeatureNumberBySubId(Long subsystemId) {
        LambdaQueryWrapper<DevopsFeature> query = new LambdaQueryWrapper<>();
        query.eq(DevopsFeature::getSubSystemId, subsystemId);
        query.orderByDesc(DevopsFeature::getFeatureNumber);
        query.last("limit 1");
        List<DevopsFeature> list = this.list(query);
        return CollectionUtils.isEmpty(list) ? 0 : list.get(0).getFeatureNumber();
    }

    @Override
    public void removeLogic(Long id, boolean logic, User currentUser) {
        if (logic){
            LambdaUpdateWrapper<DevopsFeature> update = new LambdaUpdateWrapper<>();
            update.eq(DevopsFeature::getDelFlag, SystemConstance.NOT_DELETE);
            update.eq(DevopsFeature::getId, id);
            update.set(DevopsFeature::getUpdateBy, currentUser.getId());
            update.set(DevopsFeature::getUpdateTime, LocalDateTime.now());
            update.set(DevopsFeature::getDelFlag, SystemConstance.IS_DELETE);
            this.update(update);
        }else {
            this.removeById(id);
        }
    }

    @Override
    public void releaseMark(List<Long> featureIds, User currentUser, LocalDateTime now) {
        if(CollectionUtils.isEmpty(featureIds)){
            return ;
        }
        LambdaUpdateWrapper<DevopsFeature> update = new LambdaUpdateWrapper<>();
        update.set(DevopsFeature::getClearFlag, 1);
        update.set(DevopsFeature::getReleaseTime, now);
        update.set(DevopsFeature::getUpdateBy, currentUser.getId());
        update.set(DevopsFeature::getUpdateTime, now);
        update.in(DevopsFeature::getId, featureIds);
        update.eq(DevopsFeature::getClearFlag, 0);
        this.update(update);
    }

    @Override
    public List<DevopsFeature> listBeforeReleaseTime(LocalDateTime clearTime, int clearFlag) {
        LambdaQueryWrapper<DevopsFeature> query = new LambdaQueryWrapper<>();
        query.eq(DevopsFeature::getClearFlag, clearFlag);
        query.lt(DevopsFeature::getReleaseTime, clearTime);
        return this.list(query);
    }

    @Override
    public void updateStatus(Long id, Integer status, Long currentUserId) {
        LambdaUpdateWrapper<DevopsFeature> update = new LambdaUpdateWrapper<>();
        update.eq(DevopsFeature::getId, id);
        update.set(DevopsFeature::getFeatureStatus, status);
        update.set(DevopsFeature::getUpdateBy, currentUserId);
        update.set(DevopsFeature::getUpdateTime, LocalDateTime.now());
        this.update(update);
    }

    @Override
    public void clear(Long id, Long currentUserId) {
        LambdaUpdateWrapper<DevopsFeature> update = new LambdaUpdateWrapper<>();
        update.eq(DevopsFeature::getId, id);
        update.set(DevopsFeature::getFeatureStatus, FeatureStatusEnum.CLEAR.getStatus());
        update.set(DevopsFeature::getClearFlag, 2);
        update.set(DevopsFeature::getUpdateBy, currentUserId);
        update.set(DevopsFeature::getUpdateTime, LocalDateTime.now());
        this.update(update);
    }
}
