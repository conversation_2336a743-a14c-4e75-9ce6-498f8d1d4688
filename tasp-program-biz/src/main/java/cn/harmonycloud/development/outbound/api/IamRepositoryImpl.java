package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.common.core.exception.BusinessException;
import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.pmp.fegin.IUserOrganizationProvider;
import cn.harmonycloud.pmp.fegin.IUserProvider;
import cn.harmonycloud.pmp.model.dto.UserResourceDeleteDto;
import cn.harmonycloud.pmp.model.dto.page.UserPage;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.pmp.model.vo.RoleVo;
import cn.harmonycloud.pmp.model.vo.UserVo;
import cn.harmonycloud.tenant.TenantContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/4 2:31 下午
 **/
@Service
public class IamRepositoryImpl implements IamRepository,ApiRepository {

    @Autowired
    private IUserProvider iUserProvider;
    @Value("${biz.dev.isLocalTest:true}")
    private boolean isLocalTest;
    @Autowired
    private IUserOrganizationProvider iUserOrganizationProvider;

    @Override
    public User getCurrentUser() {
        User user;
        if (isLocalTest) {
            user = new User();
            user.setId(1L);
            user.setUsername("admin");
            user.setName("超级管理员");
        } else {
            user = feignExecute(() -> iUserProvider.getByToken());
        }
        if (user == null || user.getId() == null) {
            throw new BusinessException("登录异常");
        }
        return user;
    }

    @Override
    public User getUserById(Long userId) {
        return feignExecute(() -> iUserProvider.getById(userId));
    }

    @Override
    public Map<Long, User> listUserByIdsForMap(List<Long> userIds) {
        return this.listUserByIds(userIds).stream().collect(Collectors.toMap(User::getId, u -> u));
    }

    @Override
    public List<User> listUserByIds(List<Long> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return new ArrayList<>();
        }
        List<Long> collect = userIds.stream().distinct().filter(userId -> userId!=null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect))
        {
            return new ArrayList<>();
        }
        return feignExecute(() -> iUserProvider.getByIds(collect));
    }

    @Override
    public List<UserVo> usersByCurrentOrigin(String queryParam) {
        String tenantId = TenantContextHolder.getTenantId();
        List<UserVo> result = feignExecute(() -> iUserProvider.getVoByOrganId(Long.parseLong(tenantId),queryParam));
        return result;
    }

    @Override
    public List<UserVo> resourceMember(String resourceTypeCode,Long resourceInstanceId) {
        ResourceRole resourceRole = new ResourceRole();
        resourceRole.setResourceTypeCode(resourceTypeCode);
        resourceRole.setResourceInstanceId(resourceInstanceId);
        return feignExecute(()->iUserProvider.getVosByResource(resourceRole));
    }

    @Override
    public List<UserVo> resourceMemberAdmin(String resourceTypeCode, Long resourceInstanceId) {
        List<UserVo> userVos = this.resourceMember(resourceTypeCode, resourceInstanceId);
        if(CollectionUtils.isEmpty(userVos)){
            return new ArrayList<>();
        }
        return userVos.stream().filter(user -> {
            List<RoleVo> roles = user.getRoles();
            if (CollectionUtils.isEmpty(roles)) {
                return false;
            }
            for (RoleVo role : roles) {
                if (role.getBolAdmin() == 1) {
                    return true;
                }
            }
            return false;
        }).collect(Collectors.toList());
    }

    @Override
    public List<UserVo> usersByCurrentOriginOver(String resourceTypeCode, Long instanceId, String queryParam) {
        List<UserVo> allUser = usersByCurrentOrigin(queryParam);
        List<UserVo> systemMember = resourceMember(resourceTypeCode, instanceId);
        return userOver(allUser, systemMember);
    }

    @Override
    public List<UserVo> userOver(List<UserVo> allUser, List<UserVo> overUser) {
        if (CollectionUtils.isEmpty(overUser)) {
            return allUser;
        }
        Map<Long, UserVo> collect = allUser.stream().collect(Collectors.toMap(UserVo::getId, d -> d));
        for (UserVo userInfoDto : overUser) {
            collect.remove(userInfoDto.getId());
        }
        return collect.values().stream().collect(Collectors.toList());
    }

    @Override
    public Page<UserVo> pageMember(String resourceTypeCode, Long resourceInstanceId, int current, int size, String queryParam) {
        UserPage userPage = new UserPage();
        userPage.setResourceTypeCode(resourceTypeCode);
        userPage.setResourceInstanceId(resourceInstanceId);
        userPage.setCurrent(current);
        userPage.setSize(size);
        userPage.setQueryParam(queryParam);
        return feignExecute(()->iUserProvider.page(userPage));
    }

    @Override
    public Boolean deleteMember(String resourceTypeCode, Long instanceId, Long userId) {
        UserResourceDeleteDto dto = new UserResourceDeleteDto();
        dto.setResourceTypeCode(resourceTypeCode);
        dto.setResourceInstanceId(instanceId);
        dto.setUserId(userId);
        return feignExecute(()->iUserOrganizationProvider.resourceDelete(dto));
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_APP_MANAGE_FAIL;
    }
}
