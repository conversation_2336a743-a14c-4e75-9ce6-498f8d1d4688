package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.VersionRepository;
import cn.harmonycloud.development.outbound.db.mapper.VersionMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.entity.VersionSubsystem;
import cn.harmonycloud.development.pojo.vo.version.VersionQuery;
import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemQuery;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/24 9:40 上午
 **/
@Service
public class VersionRepositoryImpl extends BaseRepositoryImpl<VersionMapper, VersionManagement> implements VersionRepository {


    @Override
    public void deleteLogic(Long versionId) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>()
                .eq(VersionManagement::getId, versionId)
                .set(VersionManagement::getDeleteStatus, SystemConstance.IS_DELETE);
        this.update(null, query);
    }

    @Override
    public VersionManagement getLastVersion(Long subsystemId, Integer versionType, String versionNumber) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getSubSystemId, subsystemId);
        query.eq(VersionManagement::getVersionType, versionType);
        query.eq(VersionManagement::getVersionNumber, versionNumber);
        query.orderByDesc(VersionManagement::getCreateTime);
        query.last("limit 1");
        return getOne(query);
    }

    @Override
    public List<VersionManagement> listByParams(Long subSystemId, String versionNumber, @Nullable String patchNumber) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getSubSystemId, subSystemId);
        query.eq(StringUtils.isNotEmpty(versionNumber), VersionManagement::getVersionNumber, versionNumber);
        query.eq(StringUtils.isNotEmpty(patchNumber), VersionManagement::getPatchNumber, patchNumber);
        query.eq(VersionManagement::getDeleteStatus, SystemConstance.NOT_DELETE);
        query.orderByDesc(VersionManagement::getCreateTime);
        return list(query);
    }

    @Override
    public List<VersionManagement> listByStatus(Long subSystemId, String versionNumber, @Nullable Integer switchStatus) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getSubSystemId, subSystemId);
        query.eq(StringUtils.isNotEmpty(versionNumber), VersionManagement::getVersionNumber, versionNumber);
        query.eq(switchStatus != null, VersionManagement::getSwitchStatus, switchStatus);
        query.eq(VersionManagement::getDeleteStatus, SystemConstance.NOT_DELETE);
        query.orderByDesc(VersionManagement::getCreateTime);
        return list(query);
    }

    @Override
    public List<VersionManagement> listByParams(Long subSystemId, Integer limit) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getSubSystemId, subSystemId);
        query.eq(VersionManagement::getDeleteStatus, SystemConstance.NOT_DELETE);
        query.orderByDesc(VersionManagement::getCreateTime);
        if (limit != null){
            query.last("limit " + limit);
        }
        return list(query);
    }

    @Override
    public Page<VersionManagement> pageQuery(VersionQuery request) {
        Page<VersionManagement> page = new Page<>(request.getPageNo(), request.getPageSize());
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getDeleteStatus, SystemConstance.NOT_DELETE);
        query.eq(VersionManagement::getSubSystemId, request.getSubsystemId());
        query.like(StringUtils.isNotEmpty(request.getVersionNumber()), VersionManagement::getVersionNumber, request.getVersionNumber());
        query.eq(request.getVersionStatus() != null, VersionManagement::getVersionStatus, request.getVersionStatus());
        query.orderByDesc(VersionManagement::getCreateTime);
        return this.page(page, query);
    }

    @Override
    public VersionManagement last(Long subsystemId) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getDeleteStatus, SystemConstance.NOT_DELETE);
        query.eq(VersionManagement::getSubSystemId, subsystemId);
        query.orderByDesc(VersionManagement::getCreateTime);
        query.last("limit 1");
        return this.getOne(query);
    }

    @Override
    public List<VersionManagement> listByParams(List<Long> ids, List<Long> subsystemIds) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getDeleteStatus, SystemConstance.NOT_DELETE);
        query.in(CollectionUtils.isNotEmpty(ids), VersionManagement::getId, ids);
        query.in(CollectionUtils.isNotEmpty(subsystemIds), VersionManagement::getSubSystemId, subsystemIds);
        return this.list(query);
    }

    @Override
    public List<VersionManagement> versionList(String version) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.like(VersionManagement::getVersionNumber, version);
        return this.list(query);
    }

    @Override
    public List<VersionManagement> listByMajorVersionId(Long majorVersionId) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getMajorVersionId , majorVersionId);
        query.eq(VersionManagement::getDeleteStatus , SystemConstance.NOT_DELETE);
        query.orderByDesc(VersionManagement::getCreateTime);
        return  this.list(query);
    }

    @Override
    public VersionManagement getOpened(Long majorVersionId) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getMajorVersionId , majorVersionId);
        query.eq(VersionManagement::getSwitchStatus , SystemConstance.SwitchStaus.OPENED);
        query.eq(VersionManagement::getDeleteStatus , SystemConstance.NOT_DELETE);
        return this.getOne(query);
    }

    @Override
    public void closeAll(Long majorVersionId, Long updateId , LocalDateTime now) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getMajorVersionId , majorVersionId);
        query.eq(VersionManagement::getSwitchStatus , SystemConstance.SwitchStaus.OPENED);
        query.set(VersionManagement::getSwitchStatus , SystemConstance.SwitchStaus.CLOSED);
        query.set(VersionManagement::getUpdateBy,updateId);
        query.set(VersionManagement::getUpdateTime,now);
        this.update(query);
    }

    @Override
    public VersionManagement getByParams(String totalVersionNumber, Long subsystemId) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getTotalVersionNumber , totalVersionNumber);
        query.eq(VersionManagement::getSubSystemId , subsystemId);
        query.eq(VersionManagement::getDeleteStatus , SystemConstance.NOT_DELETE);
        return this.getOne(query);
    }

    @Override
    public List<VersionManagement> listByMajorVersionIds(List<Long> majorVersionIds, Integer switchStatus) {
        if(CollectionUtils.isEmpty(majorVersionIds)){
            return new ArrayList<>();
        }
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.in(VersionManagement::getMajorVersionId , majorVersionIds);
        query.eq(switchStatus != null, VersionManagement::getSwitchStatus , switchStatus);
        query.eq(VersionManagement::getDeleteStatus , SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public boolean deleteLogicByMajorId(Long majorVersionId) {
        LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
        query.eq(VersionManagement::getMajorVersionId , majorVersionId);
        query.eq(VersionManagement::getDeleteStatus , SystemConstance.NOT_DELETE);
        query.set(VersionManagement::getDeleteStatus , SystemConstance.IS_DELETE);
        return this.update(query);
    }

    @Override
    public Map<Long, VersionManagement> mapOpened(List<Long> majorIds) {
        if(CollectionUtils.isNotEmpty(majorIds)){
            LambdaUpdateWrapper<VersionManagement> query = new LambdaUpdateWrapper<VersionManagement>();
            query.in(VersionManagement::getMajorVersionId , majorIds);
            query.eq(VersionManagement::getSwitchStatus , SystemConstance.SwitchStaus.OPENED);
            query.eq(VersionManagement::getDeleteStatus , SystemConstance.NOT_DELETE);
            List<VersionManagement> list = this.list(query);
            Map<Long, List<VersionManagement>> collect = list.stream().collect(Collectors.groupingBy(VersionManagement::getMajorVersionId));
            collect.forEach((k,v)->{
                if(v != null && v.size() > 1){
                    throw new SystemException(ExceptionCode.INNER_EXCEPTION, "版本数据错误");
                }
            });
            return list.stream().collect(Collectors.toMap(VersionManagement::getMajorVersionId, v -> v));
        }
        return new HashMap<>();
    }
}
