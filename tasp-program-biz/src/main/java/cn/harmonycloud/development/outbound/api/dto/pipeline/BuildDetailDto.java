package cn.harmonycloud.development.outbound.api.dto.pipeline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/25
 */
@Data
@ApiModel(value = "LastBuildVO", description = "上次构建信息视图对象")
public class BuildDetailDto {

    @ApiModelProperty("构建次数")
    private String id;

    @ApiModelProperty("构建次数")
    private String name;

    @ApiModelProperty("开始构建时间")
    private String startTimeMillis;

    @ApiModelProperty("持续时间")
    private Long durationMillis;

    @ApiModelProperty("构建结果")
    private String status;

    private String buildId;

    @ApiModelProperty("构建人")
    private String runTrigger;

    private String triggerUser;

    private String canReplay;

    private String runDescribe;

    private String gitRepo;
    @ApiModelProperty("构建分支")
    private String gitBranch;
    private String gitCommitId;
    private String gitCommitUrl;
    private String jobName;
    private Integer envId;
    private String envName;
    private String label;
    private String jobId;
    private List<JenkinsFileStartParameter> startParam;

    @ApiModelProperty("构建步骤")
    private List<StageDto> stages;


}
