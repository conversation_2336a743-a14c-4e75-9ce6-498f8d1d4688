package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.BuildInstanceRepository;
import cn.harmonycloud.development.outbound.db.mapper.BuildInstanceMapper;
import cn.harmonycloud.development.pojo.entity.BuildInstance;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Joiner;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/17 4:19 下午
 **/
@Service
public class BuildInstanceRepositoryImpl extends BaseRepositoryImpl<BuildInstanceMapper, BuildInstance> implements BuildInstanceRepository {
    @Override
    public BuildInstance getByBuildId(Long buildId) {
        LambdaQueryWrapper<BuildInstance> query = new LambdaQueryWrapper<>();
        query.eq(BuildInstance::getBuildId, buildId);
        query.last("limit 1");
        return this.baseMapper.selectOne(query);
    }

    @Override
    public BuildInstance getLastOneByParam(Long stageEnvId, Long jobId) {
        LambdaQueryWrapper<BuildInstance> query = new LambdaQueryWrapper<>();
        query.eq(BuildInstance::getStageEnvId, stageEnvId);
        query.eq(BuildInstance::getJobId, jobId);
        query.orderByDesc(BuildInstance::getCreateTime);
        query.last("limit 1");
        return this.baseMapper.selectOne(query);
    }

    @Override
    public BuildInstance getLastOneByParam(Long stageEnvId, Long jobId, @Nullable Long versionId) {
        LambdaQueryWrapper<BuildInstance> query = new LambdaQueryWrapper<>();
        query.eq(BuildInstance::getStageEnvId, stageEnvId);
        query.eq(BuildInstance::getJobId, jobId);
        query.eq(versionId!= null, BuildInstance::getMajorVersionId, versionId);
        query.isNull(versionId == null, BuildInstance::getMajorVersionId);
        query.orderByDesc(BuildInstance::getCreateTime);
        query.last("limit 1");
        return this.baseMapper.selectOne(query);
    }

    @Override
    public BuildInstance getLastOneByParam(Long stageEnvId) {
        LambdaQueryWrapper<BuildInstance> query = new LambdaQueryWrapper<>();
        query.eq(BuildInstance::getStageEnvId, stageEnvId);
        query.orderByDesc(BuildInstance::getCreateTime);
        query.last("limit 1");
        return this.baseMapper.selectOne(query);
    }

    @Override
    public BuildInstance getLastOneByParamOrNull(Long stageEnvId, @Nullable Long majorVersionId) {
        LambdaQueryWrapper<BuildInstance> query = new LambdaQueryWrapper<>();
        query.eq(BuildInstance::getStageEnvId, stageEnvId);
        query.eq(majorVersionId!= null, BuildInstance::getMajorVersionId, majorVersionId);
        query.isNull(majorVersionId == null, BuildInstance::getMajorVersionId);
        query.orderByDesc(BuildInstance::getCreateTime);
        query.last("limit 1");
        return this.baseMapper.selectOne(query);
    }

    @Override
    public void saveMergeTask(Long id, Long mergeGroupId, Long mergeTaskId, List<Long> featureIds, List<String> branches) {
        LambdaUpdateWrapper<BuildInstance> update = new LambdaUpdateWrapper<>();
        update.eq(BuildInstance::getId, id);
        update.set(BuildInstance::getMergeId, mergeGroupId);
        update.set(BuildInstance::getMergeTaskId, mergeTaskId);
        update.set(BuildInstance::getFeatures, Joiner.on(",").join(featureIds));
        update.set(CollectionUtils.isNotEmpty(branches), BuildInstance::getSourceBranches, Joiner.on(",").join(branches));
        this.update(update);
    }

    @Override
    public void removeVersion(Long versionId) {
        LambdaUpdateWrapper<BuildInstance> update = new LambdaUpdateWrapper<>();
        update.eq(BuildInstance::getVersionId, versionId);
        update.set(BuildInstance::getVersionId, null);
        this.update(update);
    }
}
