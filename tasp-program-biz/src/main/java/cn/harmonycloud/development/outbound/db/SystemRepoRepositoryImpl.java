package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.SystemRepoRepository;
import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.outbound.api.dto.repository.PromotionStrategyDTO;
import cn.harmonycloud.development.outbound.api.dto.repository.UnBindRepository;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.RepositoryFeign;
import cn.harmonycloud.development.pojo.dto.repository.SystemRepoQuery;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.vo.repository.CreateRepositoryRequest;
import cn.harmonycloud.development.outbound.api.dto.promotion.RepositoryDto;
import cn.harmonycloud.enums.ExceptionCode;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/11 4:48 下午
 **/
@Service
public class SystemRepoRepositoryImpl implements SystemRepoRepository, ApiRepository {

    @Autowired
    private RepositoryFeign repositoryFeign;



    @Override
    public void create(CreateRepositoryRequest request, DevopsSystem devopsSystem) {
        RepositoryDto repositoryDto = RepositoryDto.buildSystemProductRepo(request, devopsSystem);
        feignExecute(() -> repositoryFeign.createRepository(repositoryDto));
    }

    @Override
    public List<DevopsRepository> listByParams(SystemRepoQuery systemRepoQuery) {

        return feignExecute(() -> repositoryFeign.listAll(systemRepoQuery));
    }

    @Override
    public void delete(Long id) {
        feignExecute(() -> repositoryFeign.delete(id));
    }

    @Override
    public List<PromotionStrategyDTO> listPromotionStrategy(Long systemId, String format) {
        return feignExecute(() -> repositoryFeign.listStrategyBySystem(systemId, format));
    }

    @Override
    public List<DevopsRepository> listByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        return feignExecute(() -> repositoryFeign.listByIds(ids));
    }

    @Override
    public void unBind(Long systemId) {
        UnBindRepository unBindRepository = new UnBindRepository();
        unBindRepository.setSystemIds(List.of(systemId));
        feignExecute(() -> repositoryFeign.unbind(unBindRepository));
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_REPOSITORY_FAIL;
    }
}
