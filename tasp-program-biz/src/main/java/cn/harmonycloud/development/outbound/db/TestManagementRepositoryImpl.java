package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.TestManagementRepository;
import cn.harmonycloud.development.outbound.db.mapper.TestManagementMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.system.EnvTestDTO;
import cn.harmonycloud.development.pojo.dto.test.TestBatchModifyRequest;
import cn.harmonycloud.development.pojo.dto.test.TestModifyRequest;
import cn.harmonycloud.development.pojo.entity.TestManagement;
import cn.harmonycloud.development.pojo.vo.test.TestManagementPageQuery;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Joiner;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/28 3:30 下午
 **/
@Service
public class TestManagementRepositoryImpl extends BaseRepositoryImpl<TestManagementMapper, TestManagement> implements TestManagementRepository {
    @Override
    public Page<TestManagement> queryPage(EnvTestDTO envTestDTO) {
        Page<TestManagement> page = new Page<>(envTestDTO.getPageNo(), envTestDTO.getPageSize());
        LambdaQueryWrapper<TestManagement> query = new LambdaQueryWrapper<>();
        query.eq(TestManagement::getSubSystemId, envTestDTO.getSubSystemId());
        query.eq(null != envTestDTO.getTestStatus(), TestManagement::getTestStatus, envTestDTO.getTestStatus());
        query.like(envTestDTO.getTestCode() != null, TestManagement::getTestCode, envTestDTO.getTestCode());
        if (envTestDTO.getStartTime() != null && !envTestDTO.getStartTime().equals("")) {
            query.ge(TestManagement::getCreateTime, envTestDTO.getStartTime());
        }
        if (envTestDTO.getEndTime() != null && !envTestDTO.getEndTime().equals("")) {
            query.le(TestManagement::getCreateTime, envTestDTO.getEndTime());
        }
        query.orderByDesc(TestManagement::getCreateTime);
        return page(page, query);
    }

    @Override
    public List<TestManagement> listByParams(Long subsystemId, Long stageEnvId) {
        LambdaQueryWrapper<TestManagement> queryWrapper = new LambdaQueryWrapper<TestManagement>()
                .eq(TestManagement::getSubSystemId, subsystemId)
                .eq(stageEnvId != null, TestManagement::getStageEnvId, stageEnvId)
                .orderByDesc(TestManagement::getCreateTime);
        List<TestManagement> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<TestManagement> listByParams(Long buildInstanceId) {
        LambdaQueryWrapper<TestManagement> queryWrapper = new LambdaQueryWrapper<TestManagement>()
                .eq(TestManagement::getBuildInstanceId, buildInstanceId)
                .orderByDesc(TestManagement::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public void modify(TestModifyRequest modifyRequest, User currentUser) {
        LambdaUpdateWrapper<TestManagement> update = new LambdaUpdateWrapper<>();
        update.eq(TestManagement::getId, modifyRequest.getId());
        update.set(TestManagement::getTestStatus, modifyRequest.getTestStatus());
        update.set(TestManagement::getTestResultDescription, modifyRequest.getTestResultDescription());
        update.set(TestManagement::getSqlUpdateFlag, modifyRequest.getSqlUpdateFlag() == true? 1 : 0);
        update.set(TestManagement::getDirectorId, modifyRequest.getDirectorId());
        update.set(TestManagement::getManualResult, modifyRequest.getManualResult());
        if(CollectionUtils.isEmpty(modifyRequest.getManualReport())){
            update.set(TestManagement::getManualReport, null);
        }else{
            update.set(TestManagement::getManualReport, modifyRequest.getManualReport().get(0).getUid());
        }
        update.set(TestManagement::getAutotestResult, modifyRequest.getAutotestResult());
        if(CollectionUtils.isEmpty(modifyRequest.getAutotestReport())){
            update.set(TestManagement::getAutotestReport, null);
        }else{
            update.set(TestManagement::getAutotestReport, modifyRequest.getAutotestReport().get(0).getUid());
        }
        update.set(TestManagement::getUpdateBy, currentUser.getId());
        update.set(TestManagement::getUpdateTime, LocalDateTime.now());
        this.update(update);
    }

    @Override
    public void modify(TestBatchModifyRequest modifyRequest, User currentUser) {
        if(CollectionUtils.isEmpty(modifyRequest.getIds())){
            return ;
        }
        LocalDateTime now = LocalDateTime.now();
        List<TestManagement> tms = modifyRequest.getIds().stream().map(id -> {
            TestManagement tm = new TestManagement();
            tm.setId(id);

            String manualReport = null;
            if (CollectionUtils.isNotEmpty(modifyRequest.getManualReport())) {
                List<String> collect = modifyRequest.getManualReport().stream().map(mr -> mr.getUid()).collect(Collectors.toList());
                manualReport = Joiner.on(",").join(collect);
            }
            tm.setManualReport(manualReport);
            tm.setManualResult(modifyRequest.getManualResult());

            String autotestReport = null;
            if (CollectionUtils.isNotEmpty(modifyRequest.getAutotestReport())) {
                List<String> collect = modifyRequest.getAutotestReport().stream().map(mr -> mr.getUid()).collect(Collectors.toList());
                autotestReport = Joiner.on(",").join(collect);
            }
            tm.setAutotestReport(autotestReport);
            tm.setAutotestResult(modifyRequest.getAutotestResult());
            tm.setUpdateBy(currentUser.getId());
            tm.setUpdateTime(now);
            return tm;
        }).collect(Collectors.toList());
        this.updateBatchById(tms);
    }

    @Override
    public Page<TestManagement> pageQuery(TestManagementPageQuery pageQuery) {
        Page<TestManagement> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
        if( (pageQuery.getSystemIds() != null && pageQuery.getSystemIds().size() == 0) ||
                (pageQuery.getSubSystemIds() != null && pageQuery.getSubSystemIds().size() == 0)){
            return page;
        }
        LambdaQueryWrapper<TestManagement> query = new LambdaQueryWrapper<>();
        query.eq(TestManagement::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(CollectionUtils.isNotEmpty(pageQuery.getSystemIds()), TestManagement::getSystemId, pageQuery.getSystemIds());
        query.in(CollectionUtils.isNotEmpty(pageQuery.getSubSystemIds()), TestManagement::getSubSystemId, pageQuery.getSubSystemIds());
        query.like(StringUtils.isNotBlank(pageQuery.getTestCode()), TestManagement::getTestCode, pageQuery.getTestCode());
        query.eq(pageQuery.getTestStatus()!= null ,TestManagement::getTestStatus, pageQuery.getTestStatus());
        query.ge(StringUtils.isNotBlank(pageQuery.getStartTime()), TestManagement::getCreateTime, pageQuery.getStartTime());
        query.le(StringUtils.isNotBlank(pageQuery.getEndTime()), TestManagement::getCreateTime, pageQuery.getEndTime());
        query.orderByDesc(TestManagement::getCreateTime);
        return page(page, query);
    }
}
