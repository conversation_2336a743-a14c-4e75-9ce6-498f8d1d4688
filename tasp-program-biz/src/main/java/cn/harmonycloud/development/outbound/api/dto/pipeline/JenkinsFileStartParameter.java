package cn.harmonycloud.development.outbound.api.dto.pipeline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/1/24
 */
@Data
@ApiModel(value = "JenkinsFileStartParameter", description = "流水线启动变量")
public class JenkinsFileStartParameter {

    @ApiModelProperty("参数名")
    private String paramName;

    @ApiModelProperty("类型 string choice")
    private String type;

    @ApiModelProperty("枚举可选项")
    private List<String> choice;

    @ApiModelProperty("通过url来获取值")
    private String url;

    private  FuncEntity funcEntity;

    @ApiModelProperty("默认值")
    private String defaultValue;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("是否去除空白字符,默认否")
    private Boolean trim = Boolean.FALSE;
    @ApiModelProperty("方法参数")
    private Object value;
    @ApiModelProperty("是否展示 默认是展示")
    private Boolean showFlag = Boolean.TRUE;
    private Boolean updateFlag = Boolean.TRUE;
    private Boolean addParam = Boolean.TRUE;
    private Boolean mulFlag = Boolean.FALSE;
    @Data
    @NoArgsConstructor
    public static class FuncEntity{
        /**
         * branch 查询分支 如果流水线关联有子系统，则查询子系统分支，否则，需要gitlabId 、
         * findSubsystemBranch
         * version 参数 子系统
         * rawRepositoryName 制品库名称
         */
        private String name;
        /**
         * 参数
         */
        private Map<String,String> params;
        /**
         * 依赖属性
         */
        private List<String> depends ;


        public FuncEntity(String name){
            this.name = name;
            this.depends = new ArrayList<>(0);
        }

    }
    public JenkinsFileStartParameter(){}

    public JenkinsFileStartParameter(String key, String defaultValue){
        this.paramName = key;
        this.defaultValue = defaultValue;
        this.type = "string";
    }

}
