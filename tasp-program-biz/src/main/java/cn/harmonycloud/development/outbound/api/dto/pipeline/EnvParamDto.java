package cn.harmonycloud.development.outbound.api.dto.pipeline;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/30 6:24 下午
 **/
@Data
public class EnvParamDto implements Serializable {

    private static final long serialVersionUID = 7488125877133936573L;
    private String paramName;
    private String paramValue;
    private Boolean updateFlag = Boolean.TRUE;

    public EnvParamDto (){
        // 默认构造方法
    }

    public EnvParamDto(String paramName, String paramValue){
        this.paramName = paramName;
        this.paramValue = paramValue;
    }

    public EnvParamDto(String paramName, String paramValue, Boolean updateFlag){
        this.paramName = paramName;
        this.paramValue = paramValue;
        this.updateFlag = updateFlag;
    }

}
