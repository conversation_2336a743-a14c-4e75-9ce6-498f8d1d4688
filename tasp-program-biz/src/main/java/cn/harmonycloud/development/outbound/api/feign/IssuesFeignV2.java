package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.issue.model.dto.v2.IssuesQueryDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@FeignClient(
        name = "IssuesFeignV2",
        path = "/provider/devops/issues/v2",
        url = "${track-issues.url:http://localhost:8081/}"
)
public interface IssuesFeignV2 {

    @GetMapping({"/page"})
    @ApiOperation("工作项分页列表接口")
    BaseResult<Page<IssuesDto>> pageList(@SpringQueryMap IssuesQueryDTO issuesQueryDTO);

    @GetMapping({"/list"})
    @ApiOperation("工作项列表接口")
    BaseResult<List<IssuesDto>> list(@SpringQueryMap IssuesQueryDTO issuesQueryDTO);
}
