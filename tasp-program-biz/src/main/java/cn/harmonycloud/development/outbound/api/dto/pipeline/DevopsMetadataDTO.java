package cn.harmonycloud.development.outbound.api.dto.pipeline;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/31
 **/
@Data
public class DevopsMetadataDTO {

    @ApiModelProperty(name = "流水线 id")
    private Long jobId;

    @ApiModelProperty(name = "元数据类型1-系统 2-子系统 3-。。。")
    private Integer type;

    @ApiModelProperty(name = "元数据参数 key:value 如 systemCode:100001")
    private Map<String, Object> parameters;
}
