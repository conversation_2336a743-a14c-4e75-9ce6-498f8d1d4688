package cn.harmonycloud.development.outbound.api.dto.scm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * @ClassName MergeRequestDTO
 * @Description
 * <AUTHOR>
 * @Date 2022/3/7 3:25 PM
 **/
@Data
@ApiModel
public class MergeRequestDTO {

    @NotBlank(message = "源分支不能为空")
    @ApiModelProperty("源分支")
    private String sourceBranch;

    @NotBlank(message = "目标分支不能为空")
    @ApiModelProperty("目标分支")
    private String targetBranch;

    @NotBlank(message = "标题不能为空")
    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("评审者")
    private Integer assigneeId;

    @ApiModelProperty("评审模版id")
    private Long assigneeSettingId;

    @ApiModelProperty("评审模版内容")
    private String content;

}
