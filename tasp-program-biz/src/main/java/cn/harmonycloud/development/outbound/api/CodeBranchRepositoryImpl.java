package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.CodeBranchRepository;
import cn.harmonycloud.development.outbound.api.dto.coderepo.*;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.CodeBranchFeign;
import cn.harmonycloud.development.outbound.api.dto.scm.CreateBranchRequestDto;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/9 10:34 上午
 **/
@Service
public class CodeBranchRepositoryImpl implements CodeBranchRepository, ApiRepository {

    @Autowired
    private CodeBranchFeign codeBranchFeign;

    @Value("${cloud.scm.token:SCM@2023}")
    private String token;

    @Override
    public List<BranchDto> getBranch(Integer gitlabId, String search) {
        return feignExecute(()-> codeBranchFeign.getBranches(gitlabId, search));
    }

    @Override
    public BranchDto detailsBranch(Integer gitlabId, String branchName) {
        try {
            return customsHeaderFeignExecute(()-> codeBranchFeign.detailsBranch(gitlabId, branchName), token);
        } catch (SystemException e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("分支不存在")) {
                return null;
            }
            throw e;
        }
    }

    @Override
    public BranchDto createBranch(String name, Integer gitlabId, String sourceBranch, Boolean isProtected) {
        CreateBranchRequestDto requestDto = new CreateBranchRequestDto();
        requestDto.setBranchName(name);
        requestDto.setGitlabId(gitlabId);
        requestDto.setSourceBranch(sourceBranch);
        requestDto.setIsProtected(isProtected);
        return feignExecute(()-> codeBranchFeign.createBranch(requestDto));
    }

    @Override
    public Map<String, BranchStageDto> listBranchStage(Integer gitlabId, List<String> sourceBranch, String targetBranch) {
        if(CollectionUtils.isEmpty(sourceBranch)){
            return new HashMap<>();
        }
        List<BranchDetail> branchDetails = feignExecute(() -> codeBranchFeign.branchStage(gitlabId, sourceBranch, targetBranch));
        List<BranchStageDto> o =new ArrayList<>();
        for (BranchDetail branchDetail : branchDetails) {
            BranchStageDto branchStageDto = new BranchStageDto();
            branchStageDto.setAhead(branchDetail.getAhead());
            branchStageDto.setBehind(branchDetail.getBehind());
            branchStageDto.setBaseBranch(branchDetail.getActualCompareBranch());
            branchStageDto.setName(branchDetail.getName());
            o.add(branchStageDto);
        }
        return o.stream().collect(Collectors.toMap(BranchStageDto::getName, b -> b));
    }

    @Override
    public List<RefDto> listTag(Integer gitlabId, String tagName) {
        return feignExecute(()-> codeBranchFeign.tagList(gitlabId, tagName));
    }

    @Override
    public void removeBranch(Integer gitlabId, String branchName) {
        customsHeaderFeignExecute(()-> codeBranchFeign.remove(gitlabId, branchName), token);
    }

    @Override
    public List<String> listBranchAndTag(Integer gitlabId) {
        return customsHeaderFeignExecute(()-> codeBranchFeign.listBranchAndTag(gitlabId), token);
    }

    @Override
    public List<TempBranchVO> listTempBranch(Integer gitlabId) {
        return customsHeaderFeignExecute(()-> codeBranchFeign.listTempBranch(gitlabId), token);
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_SCM_FAIL;
    }
}
