package cn.harmonycloud.development.outbound.api.dto.project;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/29 3:32 下午
 **/
public class IssuesDto {
    @ApiModelProperty("工作项id")
    private Long id;
    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("工作项编号")
    private String code;
    @ApiModelProperty("工作项名称")
    private String name;
    @ApiModelProperty("工作项类型id")
    private Long issuesClassicId;
    @ApiModelProperty("资源类型code")
    private String resourceTypeCode;
    @ApiModelProperty("工作项类型名称")
    private String issuesClassicName;
    @ApiModelProperty("状态名称")
    private String status;
    @ApiModelProperty("迭代名称")
    private String sprint;
    @ApiModelProperty("版本名称")
    private String version;
    @ApiModelProperty("优先级")
    private String priority;
    @ApiModelProperty("负责人")
    private List<ProjectUserDto> ownerList;
    @ApiModelProperty("负责人")
    private String assignedName;

    public IssuesDto() {
    }

    public Long getId() {
        return this.id;
    }

    public Long getProjectId() {
        return this.projectId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Long getIssuesClassicId() {
        return this.issuesClassicId;
    }

    public String getIssuesClassicName() {
        return this.issuesClassicName;
    }

    public String getStatus() {
        return this.status;
    }

    public String getSprint() {
        return this.sprint;
    }

    public String getVersion() {return version;}

    public String getPriority() {return priority;}

    public List<ProjectUserDto> getOwnerList() {
        return this.ownerList;
    }

    public String getAssignedName() {
        return this.assignedName;
    }

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectId(final Long projectId) {
        this.projectId = projectId;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setIssuesClassicId(final Long issuesClassicId) {
        this.issuesClassicId = issuesClassicId;
    }

    public void setIssuesClassicName(final String issuesClassicName) {
        this.issuesClassicName = issuesClassicName;
    }

    public void setStatus(final String status) {
        this.status = status;
    }

    public void setSprint(final String sprint) {
        this.sprint = sprint;
    }

    public void setVersion(String version) {this.version = version;}

    public void setPriority(String priority) {this.priority = priority;}

    public void setOwnerList(final List<ProjectUserDto> ownerList) {
        this.ownerList = ownerList;
    }

    public void setAssignedName(final String assignedName) {
        this.assignedName = assignedName;
    }

    public String getResourceTypeCode() {
        return resourceTypeCode;
    }

    public void setResourceTypeCode(String resourceTypeCode) {
        this.resourceTypeCode = resourceTypeCode;
    }

    public String toString() {
        Long var10000 = this.getId();
        return "IssuesDTO(id=" + var10000 + ", projectId=" + this.getProjectId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", issuesClassicId=" + this.getIssuesClassicId() + ", issuesClassicName=" + this.getIssuesClassicName() + ", status=" + this.getStatus() + ", sprint=" + this.getSprint() + ", version=" + this.getVersion() + ", priority=" + this.getPriority() + ", ownerList=" + this.getOwnerList() + ", assignedName=" + this.getAssignedName() + ")";
    }
}
