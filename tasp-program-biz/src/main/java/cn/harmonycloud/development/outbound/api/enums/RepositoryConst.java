package cn.harmonycloud.development.outbound.api.enums;

/**
 * <AUTHOR>
 * @Date 2022/3/17
 **/
public class RepositoryConst {

    public static final String HARBOR = "harbor";

    public static final String NEXUS = "nexus";

    public static final String MAVEN_DEFAULT_PROXY_NAME = "proxy-maven-ali";

    public static final String NPM_DEFAULT_PROXY_NAME = "proxy-npm-taobao";

    public static final String PYPI_DEFAULT_PROXY_NAME = "proxy-pypi-ali";

    public static final String GO_DEFAULT_PROXY_NAME = "proxy-go-ali";

    public static class Harbor {

        public static final String DOCKER = "docker";

        public static final String HELM = "helm";
    }

    public static final String GLOBAL_TENANT_ID = "0";

    public static final String MAVEN_DEFAULT_PROXY_URL = "https://maven.aliyun.com/repository/public";

    public static final String NPM_DEFAULT_PROXY_URL = "https://registry.npmmirror.com";

    public static final String PYPI_DEFAULT_PROXY_URL = "https://mirrors.aliyun.com/pypi";

    public static final String GO_DEFAULT_PROXY_URL = "https://mirrors.aliyun.com/goproxy";

}
