package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.OnlineOrderRepository;
import cn.harmonycloud.development.outbound.db.mapper.OnlineOrderMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.OnlineOrder;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/17 3:36 下午
 **/
@Service
public class OnlineOrderRepositoryImpl extends BaseRepositoryImpl<OnlineOrderMapper, OnlineOrder> implements OnlineOrderRepository {



    @Override
    public void updateCode(Long id, String code) {
        LambdaUpdateWrapper<OnlineOrder> update = new LambdaUpdateWrapper<>();
        update.eq(OnlineOrder::getId, id);
        update.set(OnlineOrder::getOnlineCode, code);
        this.update(update);
    }

    @Override
    public void cancel(Long id) {
        LambdaUpdateWrapper<OnlineOrder> update = new LambdaUpdateWrapper<>();
        update.eq(OnlineOrder::getId, id);
        update.set(OnlineOrder::getApplyStatus, DevelopmentConstance.OrderApplyStatus.DRAFT);
        this.update(update);
    }

    @Override
    public void deleteLogic(Long id, boolean b) {
        LambdaUpdateWrapper<OnlineOrder> update = new LambdaUpdateWrapper<>();
        update.eq(OnlineOrder::getId, id);
        update.set(OnlineOrder::getDelFlag, SystemConstance.IS_DELETE);
        this.update(update);

    }
}
