package cn.harmonycloud.development.outbound.db.mapper;

import cn.harmonycloud.development.pojo.entity.MajorVersion;
import cn.harmonycloud.development.pojo.entity.VersionSubsystem;
import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemQuery;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MajorVersionMapper extends BaseMapper<MajorVersion> {


    @InterceptorIgnore(tenantLine = "true")
    List<VersionSubsystem> versionSubsystemQuery(@Param("req") VersionSubsystemQuery query);
}
