package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.MergeTaskRepository;
import cn.harmonycloud.development.outbound.db.mapper.MergeTaskMapper;
import cn.harmonycloud.development.pojo.entity.MergeTask;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/6/7 2:36 下午
 **/
@Service
public class MergeTaskRepositoryImpl extends BaseRepositoryImpl<MergeTaskMapper, MergeTask> implements MergeTaskRepository {
    @Override
    public Long getTaskIdByParams(Long subsystemId, String envCode) {
        LambdaQueryWrapper<MergeTask> query = new LambdaQueryWrapper<MergeTask>()
                .eq(MergeTask::getSubSystemId, subsystemId)
                .eq(MergeTask::getEnvCode, envCode);
        List<MergeTask> mergeTasks = this.list(query);
        return CollectionUtils.isEmpty(mergeTasks) ? null : mergeTasks.get(0).getMergeTaskId();
    }
}
