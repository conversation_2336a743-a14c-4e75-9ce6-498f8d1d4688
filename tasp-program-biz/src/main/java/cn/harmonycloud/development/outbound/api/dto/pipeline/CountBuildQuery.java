package cn.harmonycloud.development.outbound.api.dto.pipeline;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/3 1:58 下午
 **/
@Data
public class CountBuildQuery implements Serializable {

    private static final long serialVersionUID = -4215148840502420528L;
    private List<Long> ids;
    private String startTime;
    private String endTime;
}
