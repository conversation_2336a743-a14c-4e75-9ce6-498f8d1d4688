package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.outbound.api.dto.config.ConfigCreateDTO;
import cn.harmonycloud.development.outbound.api.dto.config.ConfigEnvCreateDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "codeConfig", url = "${cloud.scm.url:http://devops-scm-service:8080}", configuration = {FeignConfig.class})
public interface CodeConfigFeign {

    @PostMapping("/api/config/create")
    BaseResult<Long> createConfig(@RequestBody ConfigCreateDTO createDTO);

    @PostMapping("/api/config/env/create")
    BaseResult createEnv(@RequestBody ConfigEnvCreateDTO configEnvCreateDTO);
}
