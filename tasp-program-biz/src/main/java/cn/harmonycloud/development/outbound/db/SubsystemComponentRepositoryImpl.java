package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.SubsystemComponentRepository;
import cn.harmonycloud.development.outbound.db.mapper.SubSystemComponentMapper;
import cn.harmonycloud.development.pojo.entity.SubSystemComponent;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/6 1:36 下午
 **/
@Service
public class SubsystemComponentRepositoryImpl extends BaseRepositoryImpl<SubSystemComponentMapper, SubSystemComponent> implements SubsystemComponentRepository {


    @Override
    public List<SubSystemComponent> listByParam(String component, String componentKey) {
        return this.listByParam(null, component, componentKey);
    }

    @Override
    public List<SubSystemComponent> listByParam(Long subSystemId, String component) {
        return this.listByParam(subSystemId, component, null);
    }

    @Override
    public List<SubSystemComponent> listByParam(List<Long> subSystemIds, String component) {
        if (CollectionUtils.isEmpty(subSystemIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubSystemComponent> query = new LambdaQueryWrapper<>();
        query.in(SubSystemComponent::getSubSystemId, subSystemIds);
        query.in(SubSystemComponent::getComponent, component);
        query.eq(SubSystemComponent::getDelFlag, 0);
        return list(query);
    }

    @Override
    public Map<Long, List<SubSystemComponent>> mapComponent(List<Long> subSystemIds, String component) {
        List<SubSystemComponent> components = listByParam(subSystemIds, component);
        return components.stream().collect(Collectors.groupingBy(SubSystemComponent::getSubSystemId));
    }

    @Override
    public List<SubSystemComponent> listByParam(Long subSystemId, String component, String componentKey) {
        LambdaQueryWrapper<SubSystemComponent> query = new LambdaQueryWrapper<>();
        query.eq(subSystemId != null, SubSystemComponent::getSubSystemId, subSystemId);
        query.eq(StringUtils.isNotEmpty(component), SubSystemComponent::getComponent, component);
        query.eq(StringUtils.isNotEmpty(componentKey), SubSystemComponent::getComponentKey, componentKey);
        query.eq(SubSystemComponent::getDelFlag, 0);
        return list(query);
    }

    @Override
    public boolean removeByParams(String component, String componentKey) {
        LambdaUpdateWrapper<SubSystemComponent> remove = new LambdaUpdateWrapper<>();
        remove.eq(SubSystemComponent::getComponent, component);
        remove.eq(SubSystemComponent::getComponentKey, componentKey);
        return this.remove(remove);
    }

    @Override
    public List<SubSystemComponent> listByParam(String componentType, List<String> componentKeys) {
        if (CollectionUtils.isEmpty(componentKeys)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubSystemComponent> query = new LambdaQueryWrapper<>();
        query.eq(SubSystemComponent::getComponent, componentType);
        query.in(SubSystemComponent::getComponentKey, componentKeys);
        query.eq(SubSystemComponent::getDelFlag, 0);
        return this.list(query);
    }

    @Override
    public void deleteComponent(Long subsystemId) {
        LambdaUpdateWrapper<SubSystemComponent> delete = new LambdaUpdateWrapper<>();
        delete.eq(SubSystemComponent::getSubSystemId, subsystemId);
        this.remove(delete);
    }

    @Override
    public void deleteComponentBySystemId(Long systemId) {
        LambdaUpdateWrapper<SubSystemComponent> delete = new LambdaUpdateWrapper<>();
        delete.eq(SubSystemComponent::getSystemId, systemId);
        this.remove(delete);
    }
}
