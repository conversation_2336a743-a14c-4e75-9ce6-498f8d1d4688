package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.outbound.api.dto.coderepo.*;
import cn.harmonycloud.development.outbound.api.dto.scm.CreateBranchRequestDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "gitGroup", url = "${cloud.scm.url:http://devops-scm-service:8080}", configuration = {FeignConfig.class})
public interface CodeBranchFeign {

    /**
     * 模糊搜索指定仓库下的分支列表
     *
     * @param gitlabId
     * @param search
     * @return
     */
    @GetMapping("/branch/list")
    BaseResult<List<BranchDto>> getBranches(@RequestParam("gitlabId") Integer gitlabId,
                                            @RequestParam("search") String search);

    /**
     * 创建分支
     *
     * @param branchRequestDto
     * @return
     */
    @PostMapping("/branch")
    BaseResult<BranchDto> createBranch(@RequestBody CreateBranchRequestDto branchRequestDto);


    /**
     * 查询分支信息
     *
     * @param gitlabId
     * @param ref
     * @return
     */
    @GetMapping("/{gitlabId}/branch")
    BaseResult<BranchDto> detailsBranch(@PathVariable Integer gitlabId, @RequestParam String ref);

    /**
     * 查询分支差异信息
     *
     * @param gitlabId
     * @param sourceBranches 源分支列表
     * @param targetBranch 目标分支
     * @return
     */
    @GetMapping("/branch/stage")
    BaseResult<List<BranchDetail>> branchStage(@RequestParam Integer gitlabId,
                                               @RequestParam List<String> sourceBranches,
                                               @RequestParam String targetBranch);

    /**
     * 查询tag列表
     *
     * @param gitlabId
     * @param tagName
     * @return
     */
    @GetMapping("/branch/tag/list")
    BaseResult<List<RefDto>> tagList(@RequestParam Integer gitlabId,
                                     @RequestParam(required = false) String tagName);

    /**
     * 删除分支
     *
     * @param gitlabId
     * @param branchName
     * @return
     */
    @DeleteMapping("/{gitlabId}/root/branch")
    BaseResult remove(@PathVariable Integer gitlabId,
                                     @RequestParam String branchName);
    /**
     * 查询代码库下的所有分支以及标签
     *
     * @param gitlabId
     * @return
     */
    @GetMapping("/listBranchAndTag")
    BaseResult<List<String>> listBranchAndTag(@RequestParam Integer gitlabId);

    /**
     * 查询临时分支
     *
     * @param gitlabId
     * @return
     */
    @GetMapping("/listTempBranch")
    BaseResult<List<TempBranchVO>> listTempBranch(@RequestParam Integer gitlabId);
}
