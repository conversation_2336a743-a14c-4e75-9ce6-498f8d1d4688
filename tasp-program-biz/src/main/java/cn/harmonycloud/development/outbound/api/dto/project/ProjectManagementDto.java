package cn.harmonycloud.development.outbound.api.dto.project;


import cn.harmonycloud.issue.model.IssuesFieldDTO;
import cn.harmonycloud.project.model.vo.BaseVO;
import cn.harmonycloud.project.model.vo.MilestoneVO;
import cn.harmonycloud.project.model.vo.ProjectQueryVO;
import cn.harmonycloud.project.model.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/8/29 3:06 下午
 **/
public class ProjectManagementDto {

    private Long id;
    private String projectCode;
    private String name;
    private String description;
    private String belongOrgId;
    private BaseVO processDictionary;
    private BaseVO typeDictionary;
    private BaseVO sourceDictionary;
    private String sourceId;
    private BaseVO statusDictionary;
    private List<UserVO> principalList;
    private List<UserVO> assistantPrincipalList;
    private String createTime;
    private Long templateId;
    private String startTime;
    private String endTime;
    private String projectCycle;
    private String principalName;
    private String assistantPrincipalName;
    private BigDecimal schedule;
    private Long processDictionaryId;
    private Long typeDictionaryId;
    private Long sourceDictionaryId;
    private Long statusDictionaryId;
    private String queryParam;
    private Long principalId;
    private Long edepId;
    private String appProjectId;
    private String createBy;
    private String statusDictionaryName;
    private MilestoneVO milestoneVO;
    private Long businessProjectId;
    private String businessProjectName;
    @ApiModelProperty(
            name = "拓展字段"
    )
    private List<IssuesFieldDTO> customFieldsList;

    public ProjectManagementDto() {
    }

    public Long getId() {
        return this.id;
    }

    public String getProjectCode() {
        return this.projectCode;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public String getBelongOrgId() {
        return this.belongOrgId;
    }

    public BaseVO getProcessDictionary() {
        return this.processDictionary;
    }

    public BaseVO getTypeDictionary() {
        return this.typeDictionary;
    }

    public BaseVO getSourceDictionary() {
        return this.sourceDictionary;
    }

    public String getSourceId() {
        return this.sourceId;
    }

    public BaseVO getStatusDictionary() {
        return this.statusDictionary;
    }

    public List<UserVO> getPrincipalList() {
        return this.principalList;
    }

    public List<UserVO> getAssistantPrincipalList() {
        return this.assistantPrincipalList;
    }

    public String getCreateTime() {
        return this.createTime;
    }

    public Long getTemplateId() {
        return this.templateId;
    }

    public String getStartTime() {
        return this.startTime;
    }

    public String getEndTime() {
        return this.endTime;
    }

    public String getProjectCycle() {
        return this.projectCycle;
    }

    public String getPrincipalName() {
        return this.principalName;
    }

    public String getAssistantPrincipalName() {
        return this.assistantPrincipalName;
    }

    public BigDecimal getSchedule() {
        return this.schedule;
    }

    public Long getProcessDictionaryId() {
        return this.processDictionaryId;
    }

    public Long getTypeDictionaryId() {
        return this.typeDictionaryId;
    }

    public Long getSourceDictionaryId() {
        return this.sourceDictionaryId;
    }

    public Long getStatusDictionaryId() {
        return this.statusDictionaryId;
    }

    public String getQueryParam() {
        return this.queryParam;
    }

    public Long getPrincipalId() {
        return this.principalId;
    }

    public Long getEdepId() {
        return this.edepId;
    }

    public String getAppProjectId() {
        return this.appProjectId;
    }

    public String getCreateBy() {
        return this.createBy;
    }

    public String getStatusDictionaryName() {
        return this.statusDictionaryName;
    }

    public MilestoneVO getMilestoneVO() {
        return this.milestoneVO;
    }

    public Long getBusinessProjectId() {
        return this.businessProjectId;
    }

    public String getBusinessProjectName() {
        return this.businessProjectName;
    }

    public List<IssuesFieldDTO> getCustomFieldsList() {
        return this.customFieldsList;
    }

    public void setId(final Long id) {
        this.id = id;
    }

    public void setProjectCode(final String projectCode) {
        this.projectCode = projectCode;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public void setBelongOrgId(final String belongOrgId) {
        this.belongOrgId = belongOrgId;
    }

    public void setProcessDictionary(final BaseVO processDictionary) {
        this.processDictionary = processDictionary;
    }

    public void setTypeDictionary(final BaseVO typeDictionary) {
        this.typeDictionary = typeDictionary;
    }

    public void setSourceDictionary(final BaseVO sourceDictionary) {
        this.sourceDictionary = sourceDictionary;
    }

    public void setSourceId(final String sourceId) {
        this.sourceId = sourceId;
    }

    public void setStatusDictionary(final BaseVO statusDictionary) {
        this.statusDictionary = statusDictionary;
    }

    public void setPrincipalList(final List<UserVO> principalList) {
        this.principalList = principalList;
    }

    public void setAssistantPrincipalList(final List<UserVO> assistantPrincipalList) {
        this.assistantPrincipalList = assistantPrincipalList;
    }

    public void setCreateTime(final String createTime) {
        this.createTime = createTime;
    }

    public void setTemplateId(final Long templateId) {
        this.templateId = templateId;
    }

    public void setStartTime(final String startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(final String endTime) {
        this.endTime = endTime;
    }

    public void setProjectCycle(final String projectCycle) {
        this.projectCycle = projectCycle;
    }

    public void setPrincipalName(final String principalName) {
        this.principalName = principalName;
    }

    public void setAssistantPrincipalName(final String assistantPrincipalName) {
        this.assistantPrincipalName = assistantPrincipalName;
    }

    public void setSchedule(final BigDecimal schedule) {
        this.schedule = schedule;
    }

    public void setProcessDictionaryId(final Long processDictionaryId) {
        this.processDictionaryId = processDictionaryId;
    }

    public void setTypeDictionaryId(final Long typeDictionaryId) {
        this.typeDictionaryId = typeDictionaryId;
    }

    public void setSourceDictionaryId(final Long sourceDictionaryId) {
        this.sourceDictionaryId = sourceDictionaryId;
    }

    public void setStatusDictionaryId(final Long statusDictionaryId) {
        this.statusDictionaryId = statusDictionaryId;
    }

    public void setQueryParam(final String queryParam) {
        this.queryParam = queryParam;
    }

    public void setPrincipalId(final Long principalId) {
        this.principalId = principalId;
    }

    public void setEdepId(final Long edepId) {
        this.edepId = edepId;
    }

    public void setAppProjectId(final String appProjectId) {
        this.appProjectId = appProjectId;
    }

    public void setCreateBy(final String createBy) {
        this.createBy = createBy;
    }

    public void setStatusDictionaryName(final String statusDictionaryName) {
        this.statusDictionaryName = statusDictionaryName;
    }

    public void setMilestoneVO(final MilestoneVO milestoneVO) {
        this.milestoneVO = milestoneVO;
    }

    public void setBusinessProjectId(final Long businessProjectId) {
        this.businessProjectId = businessProjectId;
    }

    public void setBusinessProjectName(final String businessProjectName) {
        this.businessProjectName = businessProjectName;
    }

    public void setCustomFieldsList(final List<IssuesFieldDTO> customFieldsList) {
        this.customFieldsList = customFieldsList;
    }



    protected boolean canEqual(final Object other) {
        return other instanceof ProjectQueryVO;
    }



    public String toString() {
        Long var10000 = this.getId();
        return "ProjectQueryVO(id=" + var10000 + ", projectCode=" + this.getProjectCode() + ", name=" + this.getName() + ", description=" + this.getDescription() + ", belongOrgId=" + this.getBelongOrgId() + ", processDictionary=" + this.getProcessDictionary() + ", typeDictionary=" + this.getTypeDictionary() + ", sourceDictionary=" + this.getSourceDictionary() + ", sourceId=" + this.getSourceId() + ", statusDictionary=" + this.getStatusDictionary() + ", principalList=" + this.getPrincipalList() + ", assistantPrincipalList=" + this.getAssistantPrincipalList() + ", createTime=" + this.getCreateTime() + ", templateId=" + this.getTemplateId() + ", startTime=" + this.getStartTime() + ", endTime=" + this.getEndTime() + ", projectCycle=" + this.getProjectCycle() + ", principalName=" + this.getPrincipalName() + ", assistantPrincipalName=" + this.getAssistantPrincipalName() + ", schedule=" + this.getSchedule() + ", processDictionaryId=" + this.getProcessDictionaryId() + ", typeDictionaryId=" + this.getTypeDictionaryId() + ", sourceDictionaryId=" + this.getSourceDictionaryId() + ", statusDictionaryId=" + this.getStatusDictionaryId() + ", queryParam=" + this.getQueryParam() + ", principalId=" + this.getPrincipalId() + ", edepId=" + this.getEdepId() + ", appProjectId=" + this.getAppProjectId() + ", createBy=" + this.getCreateBy() + ", statusDictionaryName=" + this.getStatusDictionaryName() + ", milestoneVO=" + this.getMilestoneVO() + ", businessProjectId=" + this.getBusinessProjectId() + ", businessProjectName=" + this.getBusinessProjectName() + ", customFieldsList=" + this.getCustomFieldsList() + ")";
    }
}
