package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.PermissionRepository;
import cn.harmonycloud.development.outbound.api.dto.resourceinstance.ResourceInstancesDto;
import cn.harmonycloud.development.outbound.api.dto.role.RoleBindingDto;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.AppManageFeign;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.service.mapstruct.PermissionMapstruct;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.pmp.fegin.*;
import cn.harmonycloud.pmp.model.dto.*;
import cn.harmonycloud.pmp.model.entity.ResourceInstance;
import cn.harmonycloud.pmp.model.entity.ResourceOwnerInstance;
import cn.harmonycloud.pmp.model.entity.ResourceRole;
import cn.harmonycloud.pmp.model.entity.RoleBase;
import cn.harmonycloud.pmp.model.vo.RoleVo;
import cn.harmonycloud.pmp.model.vo.UserRoleVo;
import cn.harmonycloud.tenant.TenantContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/3/27 10:39 上午
 **/
@Service
public class PermissionRepositoryImpl implements PermissionRepository,ApiRepository {

    @Autowired
    private IRoleProvider iRoleProvider;
    @Autowired
    private IUserRoleProvider iUserRoleProvider;
    @Autowired
    private IResourceInstanceProvider iResourceInstanceProvider;
    @Autowired
    private IUserOrganizationProvider iUserOrganizationProvider;
    @Autowired
    private AppManageFeign appManageFeign;
    @Autowired
    private PermissionMapstruct permissionMapstruct;

    @Override
    public List<UserRoleVo> getRolesByInstance(String resourceTypeCode, Long instanceId, List<Long> userIds) {
        ResourceOwnerInstance.UserRolesByResourceUser rolesByOrganUser = new ResourceOwnerInstance.UserRolesByResourceUser();
        rolesByOrganUser.setUserIds(userIds);
        rolesByOrganUser.setResourceTypeCode(resourceTypeCode);
        rolesByOrganUser.setResourceInstanceId(instanceId);
        return feignExecute(()-> iUserRoleProvider.getRoleByUserId(rolesByOrganUser));
    }

    @Override
    public Boolean resourceInstances(String resourceTypeCode, Long instanceId, String instanceName, String parentCode, String parentId) {
        ResourceInstancesDto instancesDto = new ResourceInstancesDto();
        instancesDto.setResourceTypeCode(resourceTypeCode);
        instancesDto.setResourceInstanceId(instanceId);
        instancesDto.setResourceInstanceName(instanceName);
        instancesDto.setParentResourceInstanceId(parentId);
        instancesDto.setParentResourceTypeCode(parentCode);
        return feignExecute(()->appManageFeign.resourceInstances(instancesDto));
    }

    @Override
    public void deleteResourceInstances(String resourceTypeCode, Long resourceInstanceId) {
        ResourceInstanceDeleteDto instancesDto = new ResourceInstanceDeleteDto();
        instancesDto.setResourceTypeCode(resourceTypeCode);
        instancesDto.setResourceInstanceId(resourceInstanceId);
        feignExecute(()-> iResourceInstanceProvider.delete(instancesDto));
    }

    @Override
    public Boolean updateResourceInstances(String resourceTypeCode, Long instanceId, String instanceName) {
        ResourceInstanceUpdateDto instancesDto = new ResourceInstanceUpdateDto();
        instancesDto.setResourceTypeCode(resourceTypeCode);
        instancesDto.setResourceInstanceId(instanceId);
        instancesDto.setResourceInstanceName(instanceName);
        return feignExecute(() -> iResourceInstanceProvider.update(instancesDto));
    }

    @Override
    public List<RoleInfoDto> roleList(String resourceTypeCode) {
        return feignExecute(()->appManageFeign.roles(resourceTypeCode));
    }

    @Override
    public RoleVo roleDetails(Long roleId) {
        return feignExecute(()->iRoleProvider.info(roleId));
    }

    @Override
    public Boolean resourceInstancesUser(UserOrganizationDto resourceUser) {
        return feignExecute(()->iUserOrganizationProvider.createResource(resourceUser));
    }

    @Override
    public Boolean resourceUpdateRole(String resourceTypeCode, Long resourceInstanceId, Long userId, List<Long> roleIds) {
        UserResourceUpdateRoleDto roleDto = new UserResourceUpdateRoleDto();
        roleDto.setResourceTypeCode(resourceTypeCode);
        roleDto.setResourceInstanceId(resourceInstanceId);
        roleDto.setUserId(userId);
        roleDto.setRoleIds(roleIds);
        return feignExecute(() -> iUserOrganizationProvider.updateRole(roleDto));
    }

    @Override
    public List<ResourceInstance> resourceList(String parentCode, String parentId, String resourceTypeCode) {
        Long tenantId = Long.parseLong(TenantContextHolder.getTenantId());
        ResourceRole resourceRole = permissionMapstruct.buildResourceRole(parentCode, parentId, resourceTypeCode, tenantId);
        return feignExecute(() -> iResourceInstanceProvider.getListOwnerResource(resourceRole));
    }

    @Override
    public List<ResourceInstance> resourceList(String resourceTypeCode) {
        return feignExecute(() -> iResourceInstanceProvider.getByOrganUserCode(resourceTypeCode));
    }

    @Override
    public RoleBindingDto getGitlabGroupRoleBindingInfo(String resourceTypeCode, String roleId) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String targetAppCode = request.getHeader(SystemConstance.AMP_APP_CODE);
        String gitlab = SystemConstance.AmpResourceTypeCode.GITLAB_GROUP;
        return getRoleBindingInfo(resourceTypeCode, roleId, targetAppCode, gitlab);
    }

    @Override
    public RoleBindingDto getGitlabRoleBindingInfo(String resourceTypeCode, String roleId) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String targetAppCode = request.getHeader(SystemConstance.AMP_APP_CODE);
        String gitlab = SystemConstance.AmpResourceTypeCode.GITLAB;
        return getRoleBindingInfo(resourceTypeCode, roleId, targetAppCode, gitlab);
    }

    @Override
    public RoleBindingDto getPipelineRoleBindingInfo(String resourceTypeCode, String roleId) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String targetAppCode = request.getHeader(SystemConstance.AMP_APP_CODE);
        String gitlab = SystemConstance.AmpResourceTypeCode.PIPELINE_JOB;
        return getRoleBindingInfo(resourceTypeCode, roleId, targetAppCode, gitlab);
    }

    @Override
    public List<UserRoleVo> getRoleByUser(String resourceTypeCode, String resourceInstanceId, Long userId) {
        ResourceOwnerInstance.UserRolesByResourceUser user = new ResourceOwnerInstance.UserRolesByResourceUser();
        user.setResourceTypeCode(resourceTypeCode);
        user.setResourceTypeCode(resourceInstanceId);
        user.setUserId(userId);
        return feignExecute(()-> iUserRoleProvider.getRoleByUserId(user));
    }

    @Override
    public Map<Long, Map<String, Boolean>> mapPermission(String resourceTypeCode, List<Long> resourceInstanceIds) {
        return feignExecute(()-> appManageFeign.mapPermission(resourceTypeCode, resourceInstanceIds));
    }

    @Override
    public Map<Long, List<RoleBase>> listRoleByUserId(Long userId, String resourceTypeCode, List<Long> resourceInstanceIds) {
        RolesByResourcesUser rolesByResourcesUser = new RolesByResourcesUser();
        rolesByResourcesUser.setUserId(userId);
        rolesByResourcesUser.setResourceTypeCode(resourceTypeCode);
        rolesByResourcesUser.setResourceInstanceIds(resourceInstanceIds);
        return feignExecute(()-> iRoleProvider.getByResourceUser(rolesByResourcesUser));
    }

    @Override
    public void modifyMemberBatch(String resourceTypeCode, List<Long> resourceInstanceIds, Long userId, Long roleId) {
        UpdateResourceUserRoleDto updateResourceUserRoleDto = new UpdateResourceUserRoleDto();
        updateResourceUserRoleDto.setResourceTypeCode(resourceTypeCode);
        updateResourceUserRoleDto.setResourceInstanceIds(resourceInstanceIds);
        updateResourceUserRoleDto.setUserId(userId);
        updateResourceUserRoleDto.setRoleId(roleId);
        feignExecute(()-> iRoleProvider.resourceUpdateUserRole(updateResourceUserRoleDto));
    }

    /**
     * 查询amp的绑定角色信息
     *
     * @return
     */
    private RoleBindingDto getRoleBindingInfo(String resourceTypeCode, String roleId, String targetAppCode, String targetResourceCode) {
        return feignExecute(()-> appManageFeign.appRoleUser(resourceTypeCode, roleId, targetAppCode, targetResourceCode));
    }

    /**
     * 查询amp的绑定角色信息列表
     *
     * @return
     */
    @Override
    public List<RoleBindingDto> listRoleBindingInfo(String resourceTypeCode, String targetResourceCode) {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = requestAttributes.getRequest();
        String targetAppCode = request.getHeader(SystemConstance.AMP_APP_CODE);
        return feignExecute(()-> appManageFeign.listRoleUser(resourceTypeCode, null, targetAppCode, targetResourceCode));
    }

    private List<RoleBindingDto> listRoleBindingInfo(String resourceTypeCode, String roleId, String targetAppCode, String targetResourceCode) {
        return feignExecute(()-> appManageFeign.listRoleUser(resourceTypeCode, roleId, targetAppCode, targetResourceCode));
    }


    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_APP_MANAGE_FAIL;
    }

    @Override
    public void batchDeleteResourceInstance(String resourceTypeCode, List<Long> resourceInstanceIds) {
        ResourceInstancesDeleteDto resourceInstancesDeleteDto = new ResourceInstancesDeleteDto();
        resourceInstancesDeleteDto.setResourceTypeCode(resourceTypeCode);
        resourceInstancesDeleteDto.setResourceInstanceIds(resourceInstanceIds);
        feignExecute(()-> iResourceInstanceProvider.deleteByInstanceIds(resourceInstancesDeleteDto));
    }

    @Override
    public boolean checkResource(String resourceTypeCode, Long resourceInstanceId, String parentTypeCode, Long parentId) {
        List<ResourceInstance> resourceInstances = this.resourceList(parentTypeCode, parentId.toString(), resourceTypeCode);
        List<ResourceInstance> collect = resourceInstances.stream().filter(r -> r.getResourceInstanceId().equals(resourceInstanceId)).collect(Collectors.toList());
        return collect.size() > 0;
    }
}
