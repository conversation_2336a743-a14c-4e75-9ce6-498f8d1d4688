package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.ProductRepository;
import cn.harmonycloud.development.outbound.api.dto.promotion.*;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.ProductFeign;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeRequestDto;
import cn.harmonycloud.development.pojo.dto.version.EnvironmentDTO;
import cn.harmonycloud.development.pojo.entity.PromotionInstanceDto;
import cn.harmonycloud.development.pojo.vo.repository.CreateInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ListInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ProductPageQuery;
import cn.harmonycloud.development.pojo.entity.PromotionNodeInstance;
import cn.harmonycloud.development.pojo.vo.version.ProductPromotionVo;
import cn.harmonycloud.enums.ExceptionCode;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/12 9:43 上午
 **/
@Service
public class ProductRepositoryImpl implements ProductRepository,ApiRepository {

    @Autowired
    private ProductFeign productFeign;


    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_REPOSITORY_FAIL;
    }


    @Override
    public List<ProductTreeDto> tree(ProductTreeRequestDto request) {
        return feignExecute(() -> productFeign.tree(request));
    }

    @Override
    public List<String> versionList(Long systemId, List<Long> collect, List<Long> subsystemIds, Integer kind) {
        return feignExecute(() -> productFeign.versionList(systemId, subsystemIds, collect, kind));
    }

    /**
     * 上传制品
     */
    @Override
    public void upload(MultipartFile[] files, Integer repositoryId, String directory, String assetFilename) {
        feignExecute(() -> productFeign.upload(files, repositoryId, directory, assetFilename));
    }


    /**
     * 制品晋级
     *
     */
    @Override
    public void promoteProduct(@RequestBody ProductPromotionVo r){
        feignExecute(() -> productFeign.promotion(r));
    }

    @Override
    public List<DetailDTO> getProductPromotionDetail(PromotionDetailRequest p){
        return feignExecute(() -> productFeign.getProductPromotionDetail(p));
    }

    /**
     * 制品晋级环境查询
     * @return
     */
    @Override
    public List<EnvironmentDTO> getProductPromotionEnvironment(){
        return feignExecute(() -> productFeign.getProductPromotionEnvironment());
    }

    @Override
    public void updateMetadataByBuildId(ProductMetadataDto req) {
        feignExecute(() -> productFeign.updateMetadataByBuildId(req));
    }

    @Override
    public List<cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto> productList(Long subSystemId, String versionNumber, String format, Integer kind) {

        return feignExecute(() -> productFeign.productList(subSystemId, versionNumber, format, kind));
    }

    @Override
    public List<DevopsProductMetadataDto> list(ProductPageQuery productPageQuery) {
        return feignExecute(() -> productFeign.list(productPageQuery));
    }

    @Override
    public List<DevopsProductMetadataDto> list(Long repoId, Long subsystemId, String format, List<String> versions) {
        return feignExecute(() -> productFeign.list(repoId, subsystemId, format, versions, null));
    }

    @Override
    public CreateInstanceRsp createInstance(CreateInstanceReq createInstanceReq) {
        return feignExecute(() -> productFeign.createInstance(createInstanceReq));
    }

    @Override
    public List<PromotionInstanceDto> listPromotionInstance(ListInstanceReq listInstanceReq) {
        return feignExecute(() -> productFeign.listPromotionInstance(listInstanceReq));
    }


    @Override
    public List<DevopsProductMetadataDto> listByIds(List<Long> productIdList) {
        if(CollectionUtils.isEmpty(productIdList)){
            return new ArrayList<>();
        }
        return feignExecute(() -> productFeign.listByIds(productIdList));
    }

    @Override
    public DevopsProductMetadataDto get(Long repoId, String productName, String productVersion) {
        return feignExecute(() -> productFeign.get(repoId, productName, productVersion));
    }
}
