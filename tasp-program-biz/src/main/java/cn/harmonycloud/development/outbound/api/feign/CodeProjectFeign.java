package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.outbound.api.dto.scm.*;
import cn.harmonycloud.development.pojo.vo.scm.GetConfigResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "tasp-scm-svc")
public interface CodeProjectFeign {

    /**
     * 获取代码仓库详情
     *
     * @param gitlabId
     * @return
     */
    @GetMapping("/project")
    BaseResult<GitProjectDto> project(@RequestParam("gitlabId") Integer gitlabId);

    /**
     * 获取组织项目
     *
     * @param
     * @return
     */
    @GetMapping("/group/projects")
    BaseResult<List<GitProjectDto>> projectList(@RequestParam Integer groupId);

    /**
     * 获取组织项目
     *
     * @param
     * @return
     */
    @GetMapping("/group/project")
    BaseResult<GitProjectDto> getProject(@RequestParam Integer groupId, @RequestParam String path);

    /**
     * 更新项目
     *
     * @param request
     * @return
     */
    @PutMapping("/project")
    BaseResult updateProject(@RequestBody UpdateProjectRequest request);

    /**
     * 新建系统代码仓库
     *
     * @param request
     * @return
     */
    @PostMapping("/project")
    BaseResult<ProjectResponse> createProject(@RequestBody ProjectRequest request);

    /**
     * 注册代码仓库
     *
     * @param registryDto
     * @return
     */
    @PostMapping("/project/registry")
    BaseResult registry(@RequestBody List<RegistryDto> registryDto);

    /**
     * 添加项目成员
     *
     * @param memberDto
     * @return
     */
    @PostMapping("/project/{gitlabId}/resource/members")
    BaseResult addProjectMember(@PathVariable Integer gitlabId, @RequestBody List<ProjectMemberDto> memberDto);

    /**
     * 删除仓库成员
     *
     * @param gitlabId 代码仓库id
     * @param username gitlab用户名
     * @return
     */
    @DeleteMapping("/project/{gitlabId}/members/{username}")
    BaseResult delProjectMember(@PathVariable Integer gitlabId, @PathVariable String username);

    /**
     * 获取制定分支最近一次扫描的信息
     *
     * @param gitlabId
     * @return
     */
    @GetMapping("/codescan/tasks/latest")
    BaseResult<ScanIssueWithHistoryDetailVO> scanIssues(@RequestParam("gitlabId") Integer gitlabId,
                                                        @RequestParam("ref") String ref);

    /**
     * 初始化代码扫描任务
     *
     * @param codeRepoId 代码仓库id
     * @param language 语言
     * @return
     */
    @PostMapping("/codescan/initByLanguage/{codeRepoId}")
    BaseResult initByLanguage(@PathVariable("codeRepoId") Integer codeRepoId, @RequestParam("language") String language);

    /**
     * 批量修改多个项目角色
     *
     * @param batchProjectMemberDto
     * @return
     */
    @PostMapping("/user/modifyAccessLevel")
    BaseResult<BatchProjectMemberResponse> updateBatchProjectMember(BatchProjectMemberDto batchProjectMemberDto);

    @GetMapping("/api/config/env/list")
    BaseResult<List<String>> envList(@RequestParam("configId") Long configId);

    @GetMapping("api/config")
    BaseResult<GetConfigResponse> getConfig(@RequestParam("configId") Long configId);

    /**
     * 查询gitlabId
     *
     * @param path
     * @return
     */
    @GetMapping("/kepler/scm/project/getIdByPath/v2")
    BaseResult<Integer> getIdByPathV2(@RequestParam String path,
                                      @RequestParam Long serverId,
                                      @RequestHeader("Authorization") String token);
}
