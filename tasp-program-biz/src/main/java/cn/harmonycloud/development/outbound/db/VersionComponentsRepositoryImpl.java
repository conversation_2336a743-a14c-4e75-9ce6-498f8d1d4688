package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.VersionComponentsRepository;
import cn.harmonycloud.development.outbound.db.mapper.VersionComponentsMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.VersionComponents;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/12 9:15 上午
 **/
@Service
public class VersionComponentsRepositoryImpl extends BaseRepositoryImpl<VersionComponentsMapper, VersionComponents> implements VersionComponentsRepository {



    @Override
    public List<Long> listFeatureIdByParams(Long versionId) {
        List<VersionComponents> versionComponents = this.listByParams(versionId, SystemConstance.VersionComponent.FEATURE, null);
        return versionComponents.stream().map(c -> Long.parseLong(c.getComponentKey())).collect(Collectors.toList());
    }

    @Override
    public List<VersionComponents> listByParams(Long versionId, @Nullable String component, @Nullable String componentKey) {
        LambdaQueryChainWrapper<VersionComponents> query = lambdaQuery();
        query.eq(VersionComponents::getVersionId, versionId);
        query.eq(StringUtils.isNotEmpty(component), VersionComponents::getComponent, component);
        query.eq(StringUtils.isNotEmpty(componentKey), VersionComponents::getComponentKey, componentKey);
        query.orderByDesc(VersionComponents::getCreateTime);
        return query.list();
    }

    @Override
    public List<VersionComponents> listByParams(List<Long> versionIds, @Nullable String component, @Nullable String componentKey) {
        if(CollectionUtils.isEmpty(versionIds)){
            return new ArrayList<>();
        }
        LambdaQueryChainWrapper<VersionComponents> query = lambdaQuery();
        query.in(VersionComponents::getVersionId, versionIds);
        query.eq(StringUtils.isNotEmpty(component), VersionComponents::getComponent, component);
        query.eq(StringUtils.isNotEmpty(componentKey), VersionComponents::getComponentKey, componentKey);
        query.orderByDesc(VersionComponents::getCreateTime);
        return query.list();
    }

    @Override
    public boolean removeByParams(Long versionId, String component, @Nullable List<String> keys) {
        LambdaUpdateWrapper<VersionComponents> remove = new LambdaUpdateWrapper<>();
        remove.eq(VersionComponents::getVersionId, versionId);
        remove.eq(VersionComponents::getComponent, component);
        remove.in(CollectionUtils.isNotEmpty(keys), VersionComponents::getComponentKey, keys);
        return this.remove(remove);
    }

    @Override
    public boolean removeBySubordination(Long versionId ,String component , List<Long> subordination) {
        LambdaUpdateWrapper<VersionComponents> query = new LambdaUpdateWrapper<>();
        query.eq(VersionComponents::getVersionId, versionId);
        query.eq(VersionComponents::getComponent, component);
        query.in(CollectionUtils.isNotEmpty(subordination) ,VersionComponents::getSubordination , subordination);
        return this.remove(query);
    }

    @Override
    public VersionComponents getSubordination(Long versionId ,String component, Long subordination) {
        LambdaUpdateWrapper<VersionComponents> query = new LambdaUpdateWrapper<>();
        query.eq(VersionComponents::getVersionId, versionId);
        query.eq(VersionComponents::getComponent, component);
        query.eq(VersionComponents::getSubordination , subordination);
        return this.getOne(query);
    }

    @Override
    public boolean removeByVersionIds(List<Long> subVersionIds) {
        if(CollectionUtils.isEmpty(subVersionIds)){
            return true;
        }
        LambdaUpdateWrapper<VersionComponents> update = new LambdaUpdateWrapper<>();
        update.in(VersionComponents::getVersionId, subVersionIds);
        return this.remove(update);
    }

    @Override
    public List<VersionComponents> getByComponentKey(Long versionId, String component, List<String> keys) {
        LambdaUpdateWrapper<VersionComponents> query = new LambdaUpdateWrapper<>();
        query.eq(VersionComponents::getVersionId, versionId);
        query.eq(VersionComponents::getComponent, component);
        query.in(CollectionUtils.isNotEmpty(keys),VersionComponents::getComponentKey,keys);
        return this.list(query);
    }
}
