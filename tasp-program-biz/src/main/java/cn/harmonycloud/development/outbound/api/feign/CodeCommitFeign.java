package cn.harmonycloud.development.outbound.api.feign;


import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.outbound.api.dto.coderepo.CodeCommit;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "codeCommit", url = "${cloud.scm.url:http://devops-scm-service:8080}", configuration = {FeignConfig.class})
public interface CodeCommitFeign {

    /**
     * 获取提交信息
     *
     * @param gitlabId
     * @return
     */
    @GetMapping("/{gitlabId}/commit/{sha}/detail")
    BaseResult<CodeCommit> detail(@PathVariable Integer gitlabId,
                                  @PathVariable String sha);

    @GetMapping("api/config/commit/detail")
    BaseResult<CodeCommit> detail(@RequestParam("configId") Long configId,
                                  @RequestParam("sha") String sha);

}
