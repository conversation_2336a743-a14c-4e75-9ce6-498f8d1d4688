package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.DevopsLabelRepository;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsLabelMapper;
import cn.harmonycloud.development.pojo.entity.DevopsLabel;
import cn.harmonycloud.development.pojo.vo.label.DevopsLabelCreateDto;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import cn.harmonycloud.pmp.fegin.ITagProvider;
import cn.harmonycloud.pmp.model.entity.Tag;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/6 4:40 下午
 **/
@Service
public class DevopsLabelRepositoryImpl extends BaseRepositoryImpl<DevopsLabelMapper, DevopsLabel> implements DevopsLabelRepository, ApiRepository {

    @Autowired
    private ITagProvider iTagProvider;

    @Override
    public void removeAndSave(DevopsLabelCreateDto request) {
        if (CollectionUtils.isEmpty(request.getLabelIds())) {
            return;
        }
        List<DevopsLabel> collect = request.getLabelIds().stream().map(labelId -> {
            DevopsLabel devopsLabel = new DevopsLabel();
            devopsLabel.setClassificationCode(request.getClassificationCode());
            devopsLabel.setInstanceId(request.getInstanceId());
            devopsLabel.setLabelId(labelId);
            return devopsLabel;
        }).collect(Collectors.toList());
        LambdaUpdateWrapper<DevopsLabel> update = new LambdaUpdateWrapper<>();
        update.eq(DevopsLabel::getInstanceId, collect.get(0).getInstanceId());
        update.in(DevopsLabel::getLabelId, request.getLabelIds());
        update.eq(DevopsLabel::getClassificationCode, collect.get(0).getClassificationCode());
        this.remove(update);
        this.saveBatch(collect);
    }

    @Override
    public void removeAndSave(String classificationCode, Long instanceId, List<Long> labelsIds) {
        LambdaUpdateWrapper<DevopsLabel> update = new LambdaUpdateWrapper<>();
        update.eq(DevopsLabel::getInstanceId, instanceId);
        update.eq(DevopsLabel::getClassificationCode, classificationCode);
        this.remove(update);
        if(CollectionUtils.isEmpty(labelsIds)){
            return ;
        }
        List<DevopsLabel> collect = labelsIds.stream().map(id -> {
            DevopsLabel devopsLabel = new DevopsLabel();
            devopsLabel.setClassificationCode(classificationCode);
            devopsLabel.setInstanceId(instanceId);
            devopsLabel.setLabelId(id);
            return devopsLabel;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }

    @Override
    public boolean removeByParams(String classificationCode, Long instanceId, Long labelId) {
        LambdaUpdateWrapper<DevopsLabel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DevopsLabel::getLabelId, labelId);
        updateWrapper.eq(DevopsLabel::getInstanceId, instanceId);
        updateWrapper.eq(DevopsLabel::getClassificationCode, classificationCode);
        return this.remove(updateWrapper);
    }

    @Override
    public List<Long> listInstanceId(String classificationCode, List<Long> labelIds) {
        if(CollectionUtils.isEmpty(labelIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DevopsLabel> query = new LambdaQueryWrapper<DevopsLabel>();
        query.eq(DevopsLabel::getClassificationCode, classificationCode);
        query.in(DevopsLabel::getLabelId, labelIds);
        return this.list(query).stream().map(dl -> dl.getInstanceId()).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<Tag>> mapLabelByInstanceId(String classificationCode, List<Long> instanceIds) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<DevopsLabel> query = new LambdaQueryWrapper<>();
        query.eq(DevopsLabel::getClassificationCode, classificationCode);
        query.in(DevopsLabel::getInstanceId, instanceIds);
        List<DevopsLabel> devopsLabels = this.list(query);
        if (CollectionUtils.isEmpty(devopsLabels)) {
            return new HashMap<>();
        }
        Set<Long> labelIds = new HashSet<>();
        for (DevopsLabel devopsLabel : devopsLabels) {
            labelIds.add(devopsLabel.getLabelId());
        }
        List<Long> collect = labelIds.stream().collect(Collectors.toList());
        List<Tag> tagsByIds = feignExecute(()-> iTagProvider.getTagsByIds(collect));

        return mapLabelByFeatureIds(instanceIds, devopsLabels, tagsByIds);
    }

    @Override
    public List<Long> listLabelByInstanceId(String classificationCode, Long instanceId) {
        LambdaQueryWrapper<DevopsLabel> query = new LambdaQueryWrapper<>();
        query.eq(DevopsLabel::getClassificationCode, classificationCode);
        query.eq(DevopsLabel::getInstanceId, instanceId);
        return this.list(query).stream().map(dl -> dl.getLabelId()).collect(Collectors.toList());
    }

    private Map<Long, List<Tag>> mapLabelByFeatureIds(List<Long> featureIds, List<DevopsLabel> devopsLabels, List<Tag> tagsByIds) {
        Map<Long, List<DevopsLabel>> collect = devopsLabels.stream().collect(Collectors.groupingBy(DevopsLabel::getInstanceId));
        Map<Long, Tag> mapTag = tagsByIds.stream().collect(Collectors.toMap(Tag::getId, tag -> tag));
        Map<Long, List<Tag>> result = new HashMap<>();
        for (Long featureId : featureIds) {
            List<DevopsLabel> orDefault = collect.get(featureId);
            if (CollectionUtils.isEmpty(orDefault)) {
                result.put(featureId, new ArrayList<>());
                continue;
            }
            List<Tag> tags = new ArrayList<>();
            for (DevopsLabel devopsLabel : orDefault) {
                Tag tag = mapTag.get(devopsLabel.getLabelId());
                tags.add(tag);
            }
            result.put(featureId, tags);
        }
        return result;
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_APP_MANAGE_FAIL;
    }
}
