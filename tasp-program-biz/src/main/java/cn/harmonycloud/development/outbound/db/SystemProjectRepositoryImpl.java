package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.IamRepository;
import cn.harmonycloud.development.outbound.SystemProjectRepository;
import cn.harmonycloud.development.outbound.db.mapper.SystemProjectMapper;
import cn.harmonycloud.development.pojo.entity.SystemProject;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/14 2:08 下午
 **/
@Service
public class SystemProjectRepositoryImpl extends BaseRepositoryImpl<SystemProjectMapper, SystemProject> implements SystemProjectRepository {

    @Autowired
    private IamRepository iamRepository;

    @Override
    public void save(Long systemId, List<Long> projectIds) {
        if(systemId == null || projectIds == null){
            return;
        }
        this.remove(new LambdaQueryWrapper<SystemProject>().eq(SystemProject::getSystemId, systemId));
        if(projectIds.size() == 0){
            // 删除所有关联关系
            return ;
        }
        Long userId = iamRepository.getCurrentUser().getId();
        LocalDateTime now = LocalDateTime.now();
        List<SystemProject> records = projectIds.stream().map(id -> {
            SystemProject systemProject = new SystemProject();
            systemProject.setSystemId(systemId);
            systemProject.setProjectId(id);
            systemProject.setCreateBy(userId);
            systemProject.setCreateTime(now);
            return systemProject;
        }).collect(Collectors.toList());
        this.saveBatch(records);
    }

    @Override
    public List<Long> listProjectByParams(Long systemId, @Nullable List<Long> projectIds) {
        LambdaQueryWrapper<SystemProject> query = new LambdaQueryWrapper<>();
        query.eq(SystemProject::getSystemId, systemId);
        query.in(CollectionUtils.isNotEmpty(projectIds), SystemProject::getProjectId, projectIds);
        return this.list(query).stream().map(sp -> sp.getProjectId()).collect(Collectors.toList());
    }

    @Override
    public List<Long> listSystemByParams(List<Long> projectIds) {
        if(CollectionUtils.isEmpty(projectIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SystemProject> query = new LambdaQueryWrapper<>();
        query.in(SystemProject::getProjectId, projectIds);
        return this.list(query).stream().map(sp -> sp.getSystemId()).collect(Collectors.toList());
    }
}
