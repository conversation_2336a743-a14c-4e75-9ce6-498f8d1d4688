package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.pojo.SystemConstance;
import org.apache.commons.lang3.StringUtils;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/4 2:57 下午
 **/
@Component
public class ApiUtil {

    public HttpServletRequest getRequestAttributes(){
        ServletRequestAttributes servletRequestAttributes =  (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 定时任务触发
        if (servletRequestAttributes == null) {
            RequestContextHolder.setRequestAttributes(new ServletRequestAttributes(new MockHttpServletRequest()));
            servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        }
        return servletRequestAttributes.getRequest();
    }

    public String getRequestToken(){
        return this.getRequestHeader(SystemConstance.OAUTH_TOKEN_HEADER);
    }

    public String getRequestTenant(){
        return this.getRequestHeader(SystemConstance.OAUTH_TENANT_HEADER);
    }

    public String getRequestHeader(String key){
        if(StringUtils.isEmpty(key)){
            return "";
        }
        return this.getRequestAttributes().getHeader(key);
    }

}
