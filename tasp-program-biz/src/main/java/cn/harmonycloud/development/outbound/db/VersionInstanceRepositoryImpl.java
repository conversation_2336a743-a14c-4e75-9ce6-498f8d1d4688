package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.VersionInstanceRepository;
import cn.harmonycloud.development.outbound.db.mapper.VersionInstanceMapper;
import cn.harmonycloud.development.pojo.entity.VersionInstance;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/4 3:40 下午
 **/
@Service
public class VersionInstanceRepositoryImpl extends BaseRepositoryImpl<VersionInstanceMapper, VersionInstance> implements VersionInstanceRepository {
}
