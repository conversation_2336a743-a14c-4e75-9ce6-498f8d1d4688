package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.common.core.utils.string.StringUtils;
import cn.harmonycloud.development.outbound.DevopsSystemRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsSystemMapper;
import cn.harmonycloud.development.outbound.util.WrapperUtil;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.system.SystemPageQuery;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/18 11:11 上午
 **/
@Service
public class DevopsSystemRepositoryImpl extends BaseRepositoryImpl<DevopsSystemMapper, DevopsSystem> implements DevopsSystemRepository {

    @Autowired
    private DevopsSystemMapper devopsSystemMapper;

    @Override
    public List<DevopsSystem> listByParams(String fullName, String sysCode) {
        LambdaQueryWrapper<DevopsSystem> query = new LambdaQueryWrapper<>();
        query.like(StringUtils.isNotEmpty(fullName), DevopsSystem::getSubFullNameCn, fullName);
        query.eq(StringUtils.isNotEmpty(sysCode), DevopsSystem::getSysCode, sysCode);
        query.eq(DevopsSystem::getDelFlag, SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public List<DevopsSystem> listByParams(String fullName) {
        LambdaQueryWrapper<DevopsSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(StringUtils.isNotEmpty(fullName), DevopsSystem::getSubFullNameCn, fullName);
        return this.list(query);
    }

    @Override
    public List<DevopsSystem> listByProject(Long projectId) {
        return devopsSystemMapper.listByProject(projectId);
    }


    @Override
    public Page<DevopsSystem> pageGeneral(SystemPageQuery pageQuery) {
        if (pageQuery.getIds() != null && pageQuery.getIds().size() == 0) {
            return new Page<>(pageQuery.getPageNo(), pageQuery.getPageSize());
        }
        Page<DevopsSystem> page = new Page<>(pageQuery.getPageNo(), pageQuery.getPageSize());
        LambdaQueryWrapper<DevopsSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(CollectionUtils.isNotEmpty(pageQuery.getIds()), DevopsSystem::getId, pageQuery.getIds());
        if (StringUtils.isNotEmpty(pageQuery.getSubFullNameCn())) {
            if (WrapperUtil.isEscape(pageQuery.getSubFullNameCn())) {
                query.apply(" sub_full_name_cn like {0} escape '" + WrapperUtil.ESCAPE_CHAR + "' ", WrapperUtil.escapeWhereLike(pageQuery.getSubFullNameCn()));
            } else {
                query.like(DevopsSystem::getSubFullNameCn, pageQuery.getSubFullNameCn());
            }
        }
        query.like(StringUtils.isNotEmpty(pageQuery.getSysCode()), DevopsSystem::getSysCode, pageQuery.getSysCode());
        query.in(CollectionUtils.isNotEmpty(pageQuery.getProjectDirectorIds()), DevopsSystem::getProjectDirectorId, pageQuery.getProjectDirectorIds());
        if (pageQuery.getTop() != null && pageQuery.getTop()) {
            query.orderByDesc(DevopsSystem::getTopSort, DevopsSystem::getCreateTime);
        } else {
            query.orderByDesc(DevopsSystem::getCreateTime);
        }
        return this.page(page, query);
    }

    @Override
    public void removeLogic(Long id, User currentUser) {
        LambdaUpdateWrapper<DevopsSystem> update = new LambdaUpdateWrapper<>();
        update.set(DevopsSystem::getDelFlag, SystemConstance.IS_DELETE);
        update.set(DevopsSystem::getUpdateBy, currentUser.getId());
        update.set(DevopsSystem::getUpdateTime, LocalDateTime.now());
        update.eq(DevopsSystem::getId, id);
        this.update(update);
    }

    @Override
    public List<DevopsSystem> listByParams(List<Long> systemIds) {
        if (CollectionUtils.isEmpty(systemIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DevopsSystem> query = new LambdaQueryWrapper<>();
        query.eq(DevopsSystem::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(DevopsSystem::getId, systemIds);
        return list(query);
    }

    @Override
    public Map<Long, DevopsSystem> mapByIds(List<Long> systemIds) {
        List<DevopsSystem> systemList = this.listByParams(systemIds);
        return systemList.stream().collect(Collectors.toMap(DevopsSystem::getId, ds -> ds));
    }

    @Override
    public void closeTop(Long systemId, User currentUser) {
        LambdaUpdateWrapper<DevopsSystem> update = new LambdaUpdateWrapper<>();
        update.set(DevopsSystem::getTopSort, 0);
        update.set(DevopsSystem::getUpdateBy, currentUser.getId());
        update.set(DevopsSystem::getUpdateTime, LocalDateTime.now());
        update.eq(DevopsSystem::getId, systemId);
        this.update(update);
    }

    @Override
    public void topById(Long systemId, User currentUser) {
        QueryWrapper<DevopsSystem> queryWrapper = new QueryWrapper<>();
        // 设置需要查询的字段及条件等
        queryWrapper.select("MAX(top_sort) as top_sort");
        DevopsSystem system = this.getOne(queryWrapper);
        LambdaUpdateWrapper<DevopsSystem> update = new LambdaUpdateWrapper<>();
        update.set(DevopsSystem::getTopSort, system.getTopSort() + 1);
        update.set(DevopsSystem::getUpdateBy, currentUser.getId());
        update.set(DevopsSystem::getUpdateTime, LocalDateTime.now());
        update.eq(DevopsSystem::getId, systemId);
        this.update(update);
    }

    @Override
    public List<DevopsSystem> listByParamsNoTenant(List<Long> systemIds) {
        if (CollectionUtils.isEmpty(systemIds)) {
            return new ArrayList<>();
        }
        return devopsSystemMapper.listByIdsIgnoreTenant(systemIds);
    }

}
