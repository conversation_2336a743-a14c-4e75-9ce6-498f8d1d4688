package cn.harmonycloud.development.outbound.api.dto.promotion;

import cn.harmonycloud.development.pojo.dto.version.EnvDTO;
import cn.harmonycloud.development.pojo.dto.version.MetaData;
import lombok.Data;

/**
 * @description:
 * @author: yx
 * @time: 2022/8/25
 */
@Data
public class DetailDTO {
    private Integer productStatus = 1;
    private Long productId;
    private String productName;
    private String repoId;
    private String repoName;
    private String repoUrl;
    private String format;
    private String path;
    private String md5;
    private String createTime;
    private EnvDTO env;
    private MetaData metadata;
}
