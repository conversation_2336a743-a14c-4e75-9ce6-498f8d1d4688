package cn.harmonycloud.development.outbound.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum RepositoryKindEnum {

    PLUGIN(0,"组织二方库（组件库）"),
    LIBRARY(1,"依赖库"),
    ARTIFACT(2,"代码制品库"),
    PRE_PROD(3,"生产前置库");

    private Integer type;
    private String desc;

    public static String getDescByType(Integer type){
        for (RepositoryKindEnum value : RepositoryKindEnum.values()) {
            if(Objects.equals(type,value.getType())){
                return value.getDesc();
            }
        }
        return "null";
    }

    public static RepositoryKindEnum getKindByType(Integer type){
        for (RepositoryKindEnum value : RepositoryKindEnum.values()) {
            if(Objects.equals(type,value.getType())){
                return value;
            }
        }
        return null;
    }
}
