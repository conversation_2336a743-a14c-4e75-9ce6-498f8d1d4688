package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.OnlineOrderSubsystemRepository;
import cn.harmonycloud.development.outbound.db.mapper.OnlineOrderSystemMapper;
import cn.harmonycloud.development.pojo.entity.OnlineOrderSubsystem;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/21 8:57 下午
 **/
@Service
public class OnlineOrderSubsystemRepositoryImpl extends BaseRepositoryImpl<OnlineOrderSystemMapper, OnlineOrderSubsystem> implements OnlineOrderSubsystemRepository {




    @Override
    public void saveOrderSystem(Long orderId, List<Long> subsystemIds) {
        LambdaUpdateWrapper<OnlineOrderSubsystem> delete = new LambdaUpdateWrapper<>();
        delete.eq(OnlineOrderSubsystem::getOrderId, orderId);
        this.remove(delete);
        if(CollectionUtils.isNotEmpty(subsystemIds)){
            List<OnlineOrderSubsystem> collect = subsystemIds.stream().map(subsystemId -> {
                OnlineOrderSubsystem onlineOrderSubsystem = new OnlineOrderSubsystem();
                onlineOrderSubsystem.setOrderId(orderId);
                onlineOrderSubsystem.setSubsystemId(subsystemId);
                return onlineOrderSubsystem;
            }).collect(Collectors.toList());
            this.saveBatch(collect);
        }
    }

    @Override
    public List<Long> getSubsystemIdByOrderId(Long orderId) {
        LambdaQueryWrapper<OnlineOrderSubsystem> query = new LambdaQueryWrapper<>();
        query.eq(OnlineOrderSubsystem::getOrderId, orderId);
        return this.list(query).stream().map(o -> o.getSubsystemId()).collect(Collectors.toList());
    }
}
