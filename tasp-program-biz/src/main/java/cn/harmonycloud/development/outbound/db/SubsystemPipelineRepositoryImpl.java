package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.SubsystemPipelineRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.JobPermission;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.JobFeign;
import cn.harmonycloud.development.outbound.db.mapper.SubSystemPipelineMapper;
import cn.harmonycloud.development.pojo.entity.SubSystemPipeline;
import cn.harmonycloud.development.pojo.vo.pipeline.PipelineBindingRequest;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import cn.harmonycloud.pmp.model.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/10 5:00 下午
 **/
@Service
public class SubsystemPipelineRepositoryImpl extends BaseRepositoryImpl<SubSystemPipelineMapper, SubSystemPipeline> implements SubsystemPipelineRepository, ApiRepository {

    @Autowired
    private JobFeign jobFeign;

    @Override
    public List<SubSystemPipeline> listByParams(Long subsystemId) {
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<SubSystemPipeline>()
                .eq(SubSystemPipeline::getSubSystemId, subsystemId);
        return this.list(query);
    }

    @Override
    public List<Long> getJobIds(Long subsystemId) {
        if (subsystemId == null) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<>();
        query.eq(SubSystemPipeline::getSubSystemId, subsystemId);
        return list(query).stream().map(sp -> sp.getPipelineJobId()).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = {Exception.class})
    public void saveJobs(Long subsystemId, List<Long> jobIds, User currentUser) {
        // 删除所有
        if (subsystemId == null || CollectionUtils.isEmpty(jobIds)) {
            return;
        }
        LambdaUpdateWrapper<SubSystemPipeline> update = new LambdaUpdateWrapper<>();
        update.eq(SubSystemPipeline::getSubSystemId, subsystemId);
        update.in(SubSystemPipeline::getPipelineJobId, jobIds);
        this.remove(update);
        LocalDateTime now = LocalDateTime.now();
        List<SubSystemPipeline> collect = jobIds.stream().map(id -> {
            SubSystemPipeline sp = new SubSystemPipeline();
            sp.setSubSystemId(subsystemId);
            sp.setPipelineJobId(id);
            sp.setViewType(0);
            sp.setCreateTime(now);
            sp.setCreateBy(currentUser.getId());
            return sp;
        }).collect(Collectors.toList());
        this.saveBatch(collect);
    }

    @Override
    public Long getSubsystemId(Long jobId) {
        if (jobId == null) {
            return null;
        }
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<>();
        query.eq(SubSystemPipeline::getPipelineJobId, jobId);
        query.last("limit 1");
        SubSystemPipeline one = this.getOne(query);
        if (one == null) {
            return null;
        }
        return one.getSubSystemId();
    }

    @Override
    public List<Long> getJobIds(List<Long> subsystemIds) {
        if (CollectionUtils.isEmpty(subsystemIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<>();
        query.in(SubSystemPipeline::getSubSystemId, subsystemIds);
        List<SubSystemPipeline> list = this.list(query);
        return list.stream().map(pi -> pi.getSubSystemId()).collect(Collectors.toList());
    }

    @Override
    public void addPermission(Map<Long, List<Long>> instanceUser) {
        Map<Long, List<Long>> longListMap = this.toJobUser(instanceUser);
        if (longListMap == null) {
            return;
        }
        JobPermission jobPermission = new JobPermission();
        jobPermission.setUserMap(longListMap);
        feignExecute(() -> jobFeign.addPermission(jobPermission));
    }

    @Override
    public void savePermission(List<Long> jobIds, List<Long> userIds, Long roleId) {
        List<Long> collect = userIds.stream().filter(userId -> !userId.equals(1L)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return ;
        }
        JobPermission jobPermission = new JobPermission();
        jobPermission.setResourceInstanceIds(jobIds);
        jobPermission.setUserIds(userIds);
        jobPermission.setRoleId(roleId);
        feignExecute(() -> jobFeign.addPermission(jobPermission));
    }

    @Override
    public void deletePermission(Map<Long, List<Long>> instanceUser) {
        Map<Long, List<Long>> longListMap = this.toJobUser(instanceUser);
        if (longListMap == null) {
            return;
        }
        JobPermission jobPermission = new JobPermission();
        jobPermission.setUserMap(longListMap);
        feignExecute(() -> jobFeign.deletePermission(jobPermission));
    }

    @Override
    public void deletePermission(List<Long> jobIds, List<Long> userIds) {
        if(CollectionUtils.isEmpty(jobIds) || CollectionUtils.isEmpty(userIds)){
            return ;
        }
        JobPermission jobPermission = new JobPermission();
        jobPermission.setResourceInstanceIds(jobIds);
        jobPermission.setUserIds(userIds);
        feignExecute(() -> jobFeign.deletePermission(jobPermission));
    }

    @Override
    public void deletePermission(Long subId, List<Long> userIds) {
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<>();
        query.eq(SubSystemPipeline::getSubSystemId, subId);
        List<SubSystemPipeline> list = this.list(query);
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(userIds)) {
            return ;
        }
        List<Long> collect = list.stream().map(sp -> sp.getPipelineJobId()).collect(Collectors.toList());
        deletePermission(collect, userIds);
    }

    @Override
    public Boolean removeByParams(Long subsystemId) {
        LambdaUpdateWrapper<SubSystemPipeline> delete = new LambdaUpdateWrapper<>();
        delete.eq(SubSystemPipeline::getSubSystemId, subsystemId);
        return this.remove(delete);
    }

    @Override
    public Boolean removeByParams(Long subsystemId, Long jobId) {
        LambdaUpdateWrapper<SubSystemPipeline> delete = new LambdaUpdateWrapper<>();
        delete.eq(SubSystemPipeline::getSubSystemId, subsystemId);
        delete.eq(SubSystemPipeline::getPipelineJobId, jobId);
        return this.remove(delete);
    }

    @Override
    public Boolean removeByParams(List<Long> jobIds) {
        if (CollectionUtils.isEmpty(jobIds)) {
            return true;
        }
        LambdaUpdateWrapper<SubSystemPipeline> delete = new LambdaUpdateWrapper<>();
        delete.in(SubSystemPipeline::getPipelineJobId, jobIds);
        return this.remove(delete);
    }

    @Override
    public List<Long> listJobIdByParam(List<Long> subsystemIds) {
        if (CollectionUtils.isEmpty(subsystemIds)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<>();
        query.in(SubSystemPipeline::getSubSystemId, subsystemIds);
        return this.list(query).stream().map(p -> p.getPipelineJobId()).collect(Collectors.toList());
    }

    @Override
    public SubSystemPipeline getbyrequest(PipelineBindingRequest request) {
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<>();
        query.eq(SubSystemPipeline::getSubSystemId,request.getSubsystemId());
        query.eq(SubSystemPipeline::getPipelineJobId,request.getJobId());
        query.last("limit 1");
        SubSystemPipeline subSystemPipeline =this.getOne(query);
        return subSystemPipeline;
    }

    /**
     *
     * 子系统-用户map -》 job-用户map
     *
     * @param instanceUser
     * @return
     */
    private Map<Long, List<Long>> toJobUser(Map<Long, List<Long>> instanceUser) {
        if (instanceUser == null || CollectionUtils.isEmpty(instanceUser.keySet())) {
            return null;
        }
        List<Long> subsystemIds = instanceUser.keySet().stream().collect(Collectors.toList());
        LambdaQueryWrapper<SubSystemPipeline> query = new LambdaQueryWrapper<>();
        query.in(SubSystemPipeline::getSubSystemId, subsystemIds);
        List<SubSystemPipeline> list = this.list(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Map<Long, List<SubSystemPipeline>> collect = list.stream().collect(Collectors.groupingBy(SubSystemPipeline::getSubSystemId));
        Map<Long, List<Long>> jobUser = new HashMap<>();
        for (Long subsystemId : collect.keySet()) {
            for (SubSystemPipeline subSystemPipeline : collect.get(subsystemId)) {
                List<Long> userIds = instanceUser.get(subsystemId);
                jobUser.put(subSystemPipeline.getPipelineJobId(), userIds);
            }
        }
        return jobUser;
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_PIPELINE_FAIL;
    }
}
