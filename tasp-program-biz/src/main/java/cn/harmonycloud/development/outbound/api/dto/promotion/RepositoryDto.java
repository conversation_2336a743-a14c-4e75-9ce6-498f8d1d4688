package cn.harmonycloud.development.outbound.api.dto.promotion;

import cn.harmonycloud.development.outbound.api.enums.RepositoryFormatEnum;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.development.pojo.vo.repository.CreateRepositoryRequest;
import cn.harmonycloud.util.SystemUtils;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/1 5:28 下午
 **/
@Data
public class RepositoryDto implements Serializable {

    private static final long serialVersionUID = -7088876861129327464L;
    private String name;
    private String format = "raw";
    private String type ;
    private String repositoryType = "hosted";
    private Integer kind = 2;
    private String subSystemCode;
    private String systemCode;
    private Long systemId;
    private Long configId;
    private Long subSystemId;
    private Long envId;
    private String description;
    @ApiModelProperty("组件仓库是否创建")
    @Schema(description = "关联仓库,true表示先建， false 表示关联已有")
    private Boolean componentCreate = true;


    /**
     * 创建系统组件库默认参数
     * @param systemId
     * @param systemCode
     * @param format
     * @return
     */
    public static RepositoryDto buildSystemRepository(Long systemId, String systemCode, String format){
        RepositoryDto dto = new RepositoryDto();
        dto.setSystemId(systemId);
        dto.setSystemCode(systemCode);
        dto.setFormat(format);
        dto.setType("nexus");
        dto.setKind(0);
        dto.setName(format + "-" + SystemUtils.getRepositorySystemCode(systemCode));
        return dto;
    }

    /**
     * 创建系统制品库库
     * @return
     */
    public static RepositoryDto buildSystemProductRepo(CreateRepositoryRequest systemRepo, DevopsSystem devopsSystem){
        String repositoryByFormat = RepositoryFormatEnum.getRepositoryByFormat(systemRepo.getFormat());
        RepositoryDto dto = new RepositoryDto();
        dto.setSystemId(systemRepo.getSystemId());
        dto.setName(systemRepo.getRepoName());
        dto.setSystemCode(devopsSystem.getSysCode());
        dto.setFormat(systemRepo.getFormat());
        dto.setType(repositoryByFormat);
        dto.setSystemId(devopsSystem.getId());
        dto.setSystemCode(devopsSystem.getSysCode());
        dto.setEnvId(systemRepo.getEnvId());
        dto.setDescription(systemRepo.getDescription());
        dto.setComponentCreate(systemRepo.getComponentCreate());
        dto.setConfigId(systemRepo.getConfigId());
        return dto;
    }



}
