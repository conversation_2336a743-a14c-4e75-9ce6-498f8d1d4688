package cn.harmonycloud.development.outbound.api.dto.scm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/24 10:40 上午
 **/
@Data
public class MergeTaskDto implements Serializable {

    private static final long serialVersionUID = 1312880189303200831L;
    @ApiModelProperty("合并分支")
    private List<MergeTaskBranchDto> branches;

    @ApiModelProperty("源分支")
    private String sourceBranch;

    @ApiModelProperty("源分支commit")
    private String sourceCommit;

    @ApiModelProperty("目标")
    private String target;

    @ApiModelProperty("目标分支")
    private String targetBranch;

    @ApiModelProperty("目标分支commit")
    private String targetCommit;

    @ApiModelProperty("是否动态分支")
    private Boolean isTargetDynamic;

    private Long taskId;

    // 初始化-INIT；RUNNING-执行中；BLOCK-阻塞中；CANCELED-已取消；SUCCEEDED-成功；ERROR-失败
    private String status;

    private String endTime;

    private String errorMsg;
}
