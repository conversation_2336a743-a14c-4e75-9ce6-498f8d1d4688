package cn.harmonycloud.development.outbound.api.dto.pipeline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/24
 */
@Data
@ApiModel(value = "BuildDTO", description = "执行构建请求对象")
public class BuildDto {

    @ApiModelProperty("启动变量")
    private List<JenkinsFileStartParameter> startParams;

    @ApiModelProperty("jobId")
    private String jobId;

    @ApiModelProperty("运行备注")
    private String runDescribe;

    private String userName;

    private String userId;
}

