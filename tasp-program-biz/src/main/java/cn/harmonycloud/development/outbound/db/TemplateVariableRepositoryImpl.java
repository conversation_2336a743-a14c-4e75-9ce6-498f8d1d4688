package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.TemplateVariableMapper;
import cn.harmonycloud.development.outbound.thgn.TemplateVariableRepository;
import cn.harmonycloud.trinasolar.model.entity.TemplateVariable;
import cn.harmonycloud.mybatis.base.BaseRepository;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;

import org.springframework.stereotype.Service;


/**
 * @Description
 * <AUTHOR> @Date 2025/03/25
 **/
@Service
public class TemplateVariableRepositoryImpl extends BaseRepositoryImpl<TemplateVariableMapper, TemplateVariable> implements TemplateVariableRepository {





}
