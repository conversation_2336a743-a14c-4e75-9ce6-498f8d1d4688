package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.CodeGroupRepository;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.CodeGroupFeign;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupMemberDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupResponse;
import cn.harmonycloud.enums.ExceptionCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/9 10:04 上午
 **/
@Service
public class CodeGroupRepositoryImpl implements CodeGroupRepository, ApiRepository {

    @Autowired
    private CodeGroupFeign codeGroupFeign;
    @Value("${cloud.scm.token:SCM@2023}")
    private String token;

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_SCM_FAIL;
    }

    @Override
    public GroupResponse createGroup(GroupRequest request) {
        return customsHeaderFeignExecute(() -> codeGroupFeign.createGroup(request), token);
    }

    @Override
    public GroupDto group(Integer groupId, String path) {
        return feignExecute(()-> codeGroupFeign.group(groupId, path));
    }

    @Override
    public List<GroupDto> getGroups() {
        return feignExecute(()-> codeGroupFeign.getSystemGroups());
    }

    @Override
    public void addGroupMember(Integer groupId, List<GroupMemberDto> members) {
        customsHeaderFeignExecute(() -> codeGroupFeign.addGroupMember(groupId, members), token);
    }

    @Override
    public void deleteGroupMember(Integer groupId, String username) {
        customsHeaderFeignExecute(() -> codeGroupFeign.delGroupMember(groupId, username), token);
    }

    @Override
    public void modifyGroupName(Integer id, String groupName) {
        customsHeaderFeignExecute(() -> codeGroupFeign.modifyGroupName(id, groupName), token);
    }
}
