package cn.harmonycloud.development.outbound.api.dto.scm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description 代码库更新请求对象实体
 * <AUTHOR>
 * @Date 2023/12/6 9:30 上午
 **/
@Data
public class ProjectDTO implements Serializable {

    @ApiModelProperty("代码仓库id")
    private Integer gitlabId;

    @ApiModelProperty("代码仓库名称")
    private String name;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("路径")
    private String path;

    @ApiModelProperty("代码仓库地址")
    private String applicationSSHAddress;

    @ApiModelProperty("代码仓库地址")
    private String applicationHTTPAddress;

    @ApiModelProperty("仓库权限等级")
    private String visibility;

    @ApiModelProperty("默认分支")
    private String defaultBranch;

    @ApiModelProperty("群组id")
    private Integer groupId;

    @ApiModelProperty("群组path")
    private String groupPath;

    @ApiModelProperty("初始化readme")
    private Boolean initializeWithReadme;

    private String projectType;
}
