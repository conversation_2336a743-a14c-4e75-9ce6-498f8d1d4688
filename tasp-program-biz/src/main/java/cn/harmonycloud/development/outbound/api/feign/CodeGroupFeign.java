package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupMemberDto;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.GroupResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "CodeGroupFeign", url = "${cloud.scm.url:http://devops-scm-service:8080}", configuration = {FeignConfig.class})
public interface CodeGroupFeign {

    /**
     * 新增组织
     *
     * @param request
     * @return
     */
    @PostMapping("/group")
    BaseResult<GroupResponse> createGroup(@RequestBody GroupRequest request);

    /**
     * 添加组织成员
     *
     * @param memberDto
     * @return
     */
    @PostMapping("/group/{groupId}/member")
    BaseResult addGroupMember(@PathVariable Integer groupId, @RequestBody List<GroupMemberDto> memberDto);


    /**
     * 删除组织成员
     *
     * @param groupId  组织id
     * @param username gitlab用户名
     * @return
     */
    @DeleteMapping("/group/{groupId}/member/{username}")
    BaseResult delGroupMember(@PathVariable Integer groupId, @PathVariable String username);

    /**
     * 查询group详情
     *
     * @param groupId 分组id或英文名
     * @return
     */
    @GetMapping("/group")
    BaseResult<GroupDto> group(@RequestParam(value = "groupId",required = false) Integer groupId,
                               @RequestParam(value = "path",required = false) String path);

    /**
     * 查询group分组
     *
     * @return
     */
    @GetMapping("/group/list")
    BaseResult<List<GroupDto>> getGroups();

    /**
     * 更新代码分组名称
     *
     * @param groupId 分组id
     * @param groupName 分组名称
     * @return
     */
    @PostMapping("/group/modifyGroupName")
    BaseResult modifyGroupName(@RequestParam Integer groupId, @RequestParam String groupName);

    /**
     * 查询group分组
     *
     * @return
     */
    @GetMapping("/group/system/list")
    BaseResult<List<GroupDto>> getSystemGroups();

}
