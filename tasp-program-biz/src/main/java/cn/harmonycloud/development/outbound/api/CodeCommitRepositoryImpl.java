package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.CodeCommitRepository;
import cn.harmonycloud.development.outbound.api.dto.coderepo.CodeCommit;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.CodeCommitFeign;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.util.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.ZoneId;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/24 10:28 上午
 **/
@Service
public class CodeCommitRepositoryImpl implements CodeCommitRepository, ApiRepository {

    @Autowired
    private CodeCommitFeign codeCommitFeign;
    @Value("${cloud.scm.token:SCM@2023}")
    private String token;


    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_SCM_FAIL;
    }

    @Override
    public CodeCommit detail(Integer gitlabId, String sha) {
        CodeCommit codeCommit = feignExecute(() -> codeCommitFeign.detail(gitlabId, sha));
        String authoredDate = DateUtils.changeZoneTime(codeCommit.getAuthoredDate(), DateUtils.DATE_FORMAT, ZoneId.systemDefault());
        String commitDate = DateUtils.changeZoneTime(codeCommit.getCommittedDate(), DateUtils.DATE_FORMAT, ZoneId.systemDefault());
        String creatAt = DateUtils.changeZoneTime(codeCommit.getCreatedAt(), DateUtils.DATE_FORMAT, ZoneId.systemDefault());
        codeCommit.setCreatedAt(creatAt);
        codeCommit.setAuthoredDate(authoredDate);
        codeCommit.setCommittedDate(commitDate);
        return codeCommit;
    }

    @Override
    public CodeCommit detail(Long configId, String sha) {
        CodeCommit codeCommit = customsHeaderFeignExecute(() -> codeCommitFeign.detail(configId, sha), token);
        String authoredDate = DateUtils.changeZoneTime(codeCommit.getAuthoredDate(), DateUtils.DATE_FORMAT, ZoneId.systemDefault());
        String commitDate = DateUtils.changeZoneTime(codeCommit.getCommittedDate(), DateUtils.DATE_FORMAT, ZoneId.systemDefault());
        String creatAt = DateUtils.changeZoneTime(codeCommit.getCreatedAt(), DateUtils.DATE_FORMAT, ZoneId.systemDefault());
        codeCommit.setCreatedAt(creatAt);
        codeCommit.setAuthoredDate(authoredDate);
        codeCommit.setCommittedDate(commitDate);
        return codeCommit;
    }
}
