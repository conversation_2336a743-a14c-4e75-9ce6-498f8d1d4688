package cn.harmonycloud.development.outbound.db.mapper;

import cn.harmonycloud.development.pojo.dto.project.ProjectRequest;
import cn.harmonycloud.development.pojo.entity.ProjectManagement;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

public interface ProjectManagementMapper extends BaseMapper<ProjectManagement> {

    Page<ProjectManagement> page(Page<ProjectManagement> page, @Param("request") ProjectRequest request);
}
