package cn.harmonycloud.development.outbound.db.mapper;

import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
* <AUTHOR>
* @description 针对表【feature_branch(特性关联分支表)】的数据库操作Mapper
* @createDate 2022-08-03 11:35:24
* @Entity cn.harmonycloud.hzbank.pojo.entity.FeatureBranch
*/
public interface FeatureBranchMapper extends BaseMapper<FeatureBranch> {


}




