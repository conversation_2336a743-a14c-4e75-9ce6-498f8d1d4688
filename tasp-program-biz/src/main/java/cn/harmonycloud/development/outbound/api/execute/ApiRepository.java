package cn.harmonycloud.development.outbound.api.execute;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.common.core.exception.UnauthorizedException;
import cn.harmonycloud.constants.ApiConstance;
import cn.harmonycloud.development.config.CloudFeignUtils;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.pmp.resp.R;
import cn.harmonycloud.util.LogUtils;
import feign.FeignException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * 微服务调用统一处理
 */
public interface  ApiRepository {


    ExceptionCode getException();

    default  <T,E> T check(E result){
        int code = 0;
        String msg = "";
        T data = null;
        if(result instanceof BaseResult){
            BaseResult<T> baseResult = (BaseResult<T>) result;
            code = baseResult.getCode();
            msg = baseResult.getMsg();
            data = baseResult.getData();
        }else if(result instanceof R){
            R<T> baseResult = (R<T>) result;
            code = baseResult.getCode();
            msg = baseResult.getMsg();
            data = baseResult.getData();
        }
        if(code == ApiConstance.OAUTH){
            throw new UnauthorizedException(msg);
        }
        if (code != ApiConstance.SUCCESS){
            throw new SystemException(getException(), getException() + ":" + msg);
        }
        return data;
    }

    default  <E,T> T closeTransferHeaderFeignExecute(FeignExecute<E> feignExecute){
        try {
            CloudFeignUtils.closeTransferHeader();
            T o = feignExecute(feignExecute);
            return o;
        }catch (Exception e){
            throw e;
        }finally {
            CloudFeignUtils.openTransferHeader();
        }
    }

    default  <E,T> T customsHeaderFeignExecute(FeignExecute<E> feignExecute, String token){
        try {
            CloudFeignUtils.setToken(token);
            T o = feignExecute(feignExecute);
            return o;
        }catch (Exception e){
            throw e;
        }finally {
            CloudFeignUtils.clearToken();
        }
    }


    @SneakyThrows
    default  <E,T> T feignExecute(FeignExecute<E> feignExecute) {
        try {
            E execute = feignExecute.execute();
            return check(execute);
        } catch (Exception throwable) {
            LogUtils.log(throwable);
            if(throwable instanceof FeignException.Unauthorized){
                throw new UnauthorizedException(throwable.getMessage());
            }
            if (throwable instanceof SystemException) {
                throw throwable;
            }
            if (throwable instanceof UnauthorizedException) {
                throw throwable;
            }
            throw new SystemException(getException(), getException() + ":" + throwable.getMessage());
        }
    }

}
