package cn.harmonycloud.development.outbound.api.feign;


import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DeployHostDTO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.outbound.api.dto.pipeline.SystemDeployEnvDTO;
import cn.harmonycloud.development.pojo.dto.operation.SystemEnv;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "deployEnvFeign", url = "${cloud.pipeline.url}")
public interface DeployEnvFeign {

    @GetMapping("/deploy/env")
    BaseResult<Page<DoDeployEnv>> page();

    @PostMapping("/deploy/host/createSystemEnv")
    BaseResult<List<SystemEnv>> createSystemEnv(@RequestBody List<SystemEnv> systemEnvList);

    @GetMapping("/deploy/env/list")
    BaseResult<List<DoDeployEnv>> list(@RequestParam List<Long> ids);

    @GetMapping("/deploy/host/listByIds")
    BaseResult<List<DeployHostDTO>> listHostByIds(@RequestParam List<Integer> ids);

    @GetMapping("/deploy/env/systemEnvList")
    BaseResult<List<SystemDeployEnvDTO>> listBySystem(@RequestParam Long systemId);

    @GetMapping("/deploy/env/appEnvs")
    BaseResult<List<DoDeployEnv>> listBySystemId(@RequestParam Long systemId);
}
