package cn.harmonycloud.development.outbound.api.dto.repository;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 4:45 下午
 **/
@Data
public class PromotionStrategyDTO {

    private Long id;
    /**
     * 策略名称
     */
    private String name;

    /**
     * 仓库类型
     */
    private String format;

    /**
     * 系统id
     */
    private Long systemId;

    private List<PromotionNodeDTO> promotionNodes;

    private Long createBy;

    private LocalDateTime createTime;

    private Long updateBy;

    private LocalDateTime updateTime;

}
