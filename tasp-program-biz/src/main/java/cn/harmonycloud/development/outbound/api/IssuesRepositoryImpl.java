package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.IssuesRepository;
import cn.harmonycloud.development.outbound.api.dto.project.IssuesDto;
import cn.harmonycloud.development.outbound.api.dto.project.ProjectUserDto;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.IssuesFeignV2;
import cn.harmonycloud.development.service.mapstruct.ProjectMapstruct;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.exception.SystemException;
import cn.harmonycloud.issue.model.DevopsIssuesDTO;
import cn.harmonycloud.issue.model.dto.v2.IssuesQueryDTO;
import cn.harmonycloud.pmp.model.entity.User;
import cn.harmonycloud.project.model.dto.IssuesDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/9 1:41 下午
 **/
@Service
public class IssuesRepositoryImpl implements IssuesRepository, ApiRepository {

    @Value("${devops-project-svc.apiVersion:v2}")
    private String apiVersion;

//    @Autowired
//    private IssuesProvider issuesProvider;
//
//    @Autowired
//    private IssuesProviderV2 issuesProviderV2;

//    @Autowired
//    private cn.harmonycloud.trackissues.provider.IssuesProvider trackIssuesProvider;

    @Autowired
    private IssuesFeignV2 issuesFeignV2;

    @Autowired
    private ProjectMapstruct projectMapstruct;

//    @Autowired
//    private CustomizedIssuesProvider customizedIssuesProvider;
//
//    @Autowired
//    private IssuesTypeProvider issuesTypeProvider;

    private static final Map<String, Long> issuesTypeMap = Maps.newConcurrentMap();


    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_PROJECT_FAIL;
    }

    @Override
    public List<IssuesDto> listTask(List<Long> taskIds){
//        if(CollectionUtils.isEmpty(taskIds)){
//            return new ArrayList<>();
//        }
//        if("v2".equals(apiVersion)){
//          IssuesQueryDTO query = new IssuesQueryDTO();
//            query.setIdList(taskIds);
//            query.setIsBase(false);
//            List<IssuesDto> issuesDtos = feignExecute(() -> issuesFeignV2.list(query));
//            return issuesDtos.stream().map(issues -> {
//                if (StringUtils.isNotEmpty(issues.getAssignedName())){
//                    List<ProjectUserDto> collect = Splitter.on(",").splitToStream(issues.getAssignedName()).map(n -> {
//                        ProjectUserDto userDTO = new ProjectUserDto();
//                        userDTO.setUserName(n);
//                        return userDTO;
//                    }).collect(Collectors.toList());
//                    issues.setOwnerList(collect);
//                }
//                return issues;
//            }).collect(Collectors.toList());
//        }else if("v1".equals(apiVersion)){
//            IssuesQueryDTO query = new IssuesQueryDTO();
//            query.setIdList(taskIds);
//            List<IssuesDTO> record = feignExecute(() ->issuesProvider.list(query));
//            return record.stream().map(is -> v1ToIssues(is)).collect(Collectors.toList());
//        }
        throw new SystemException(ExceptionCode.REMOTE_PROJECT_FAIL, "api版本错误");
    }

//    private IssuesDto v1ToIssues(IssuesDTO issues){
//        IssuesDto issuesDto = projectMapstruct.v1ToIssues(issues);
//        List<UserDTO> ownerList = issues.getOwnerList();
//        if(CollectionUtils.isNotEmpty(ownerList)){
//            List<ProjectUserDto> collect = ownerList.stream().map(u -> projectMapstruct.v1ToUser(u)).collect(Collectors.toList());
//            issuesDto.setOwnerList(collect);
//        }
//        return issuesDto;
//    }

    private IssuesDto v2ToIssues(DevopsIssuesDTO issues){
        IssuesDto issuesDto = projectMapstruct.v2ToIssues(issues);
        if (StringUtils.isNotEmpty(issues.getAssignedName())){
            List<ProjectUserDto> collect = Splitter.on(",").splitToStream(issues.getAssignedName()).map(n -> {
                ProjectUserDto userDTO = new ProjectUserDto();
                userDTO.setUserName(n);
                return userDTO;
            }).collect(Collectors.toList());
            issuesDto.setOwnerList(collect);
        }
        return issuesDto;
    }

    @Override
    public List<IssuesDto> listTask(Long projectId, List<Long> directors, List<Long> issuesClassicIdList, Boolean isBase){

//        if("v2".equals(apiVersion)){
//            List<Long> projectIds = new ArrayList<>();
//            projectIds.add(projectId);
//            IssuesQueryDTO query = new IssuesQueryDTO();
//            query.setProjectIdList(projectIds);
//            query.setPrincipalIdList(directors);
//            if(isBase != null){
//                query.setIsBase(isBase);
//            }
//            if (CollectionUtils.isEmpty(issuesClassicIdList)){
//                List<DevopsIssuesDTO> issuesLis = feignExecute(() ->issuesProviderV2.list(query));
//                return issuesLis.stream().map(is -> v2ToIssues(is)).collect(Collectors.toList());
//            }
//
//            List<DevopsIssuesDTO> issuesList = new ArrayList<>();
//            for (Long aLong : issuesClassicIdList) {
//                if (aLong.equals(1L)){
//                    query.setIssuesTypeId(96L);
//                }
//                if (aLong.equals(2L)){
//                    query.setIssuesTypeId(97L);
//                }
//                if (aLong.equals(3L)){
//                    query.setIssuesTypeId(98L);
//                }
//                List<DevopsIssuesDTO> data = feignExecute(() ->issuesProviderV2.list(query));
//                if (data != null){
//                    issuesList.addAll(data);
//                }
//            }
//            return issuesList.stream().map(is -> v2ToIssues(is)).collect(Collectors.toList());
//        }else if("v1".equals(apiVersion)){
//            List<Long> projectIds = new ArrayList<>();
//            projectIds.add(projectId);
//            IssuesQueryDTO query = new IssuesQueryDTO();
//            query.setProjectIdList(projectIds);
//            query.setIssuesClassicIdList(issuesClassicIdList);
//            query.setPrincipalIdList(directors);
//          //  List<IssuesDTO> data = feignExecute(() ->issuesProvider.list(query));
//            return data.stream().map(is -> v1ToIssues(is)).collect(Collectors.toList());
//        }
        throw new SystemException(ExceptionCode.REMOTE_PROJECT_FAIL, "api版本错误");
    }

    @Override
    public Page<IssuesDto> pageTask(Long projectId, String title,Long sprintId,Long versionId, Long issuesTypeId, Long statusId, Long priorityid, List<Long> principalIdList,  List<Long> taskIds, int page, int size){
//        if("v2".equals(apiVersion)){
//            IssuesQueryDTO query = new IssuesQueryDTO();
//            query.setName(title);
//            query.setExcludeIdList(taskIds);
//            query.setPriorityId(priorityid);
//            query.setSprintId(sprintId);
//            query.setStatusId(statusId);
//            query.setVersionId(versionId);
//            query.setIssuesTypeId(issuesTypeId);
//            query.setPrincipalIdList(principalIdList);
//            query.setPageNo(page);
//            query.setPageSize(size);
//            query.setIsBase(false);
//            if(projectId != null){
//                List<Long> longs = Lists.newArrayList(projectId);
//                query.setProjectIdList(longs);
//            }
//            Page<DevopsIssuesDTO> result = feignExecute(() ->issuesProviderV2.pageList(query));
//            return PageUtils.exchangeRecord(result, i -> v2ToIssues(i));
//        }else if("v1".equals(apiVersion)){
//            IssuesQueryDTO query = new IssuesQueryDTO();
//            query.setName(title);
//            query.setExcludeIdList(taskIds);
//            query.setPageNo(page);
//            query.setPageSize(size);
//            if(projectId != null){
//                List<Long> longs = Lists.newArrayList(projectId);
//                query.setProjectIdList(longs);
//            }
//            Page<IssuesDTO> result = feignExecute(() ->issuesProvider.pageList(query));
//            return PageUtils.exchangeRecord(result, i -> v1ToIssues(i));
//        }
        throw new SystemException(ExceptionCode.REMOTE_PROJECT_FAIL, "api版本错误");
    }

    @Override
    public List<IssuesDTO> getListByIds(String issuesTypeCode, List<Long> ids) {
        return null;
    }

    @Override
    public Boolean updateIssuesOfSys(IssuesDTO issuesDTO, User currentUser) {
        return null;
    }

    @Override
    public List<IssuesDTO> listIssues(IssuesQueryDTO issuesQueryDTO) {
        return null;
    }

//    @Override
//    public List<IssuesDTO> getListByIds(String issuesTypeCode, List<Long> ids) {
//        if(CollectionUtils.isEmpty(ids)){
//            return new ArrayList<>();
//        }
//        IssuesQueryDTO issuesQueryDTO = new IssuesQueryDTO();
//        issuesQueryDTO.setIds(ids);
//        issuesQueryDTO.setIssuesTypeId(getIssuesTypeIdByName(issuesTypeCode));
//        return feignExecute(() -> customizedIssuesProvider.getListByIds(issuesQueryDTO));
//    }

//    public Boolean updateIssuesOfSys(IssuesDTO issuesDTO, User currentUser){
//        issuesDTO.setUpdateBy(currentUser.getId());
//        issuesDTO.setUpdateTime(LocalDateTime.now());
//        return feignExecute(() ->trackIssuesProvider.updateIssuesOfSys(issuesDTO.getId(), issuesDTO));
//    }


//    protected Long getIssuesTypeIdByName(String code){
//        if(issuesTypeMap.get(code) == null){
//         //   IssuesTypeDTO issuesTypeDTO = feignExecute(() -> issuesTypeProvider.detailByCode(code));
//            issuesTypeMap.put(code, issuesTypeDTO.getId());
//        }
//        return issuesTypeMap.get(code);
//    }

//    @Override
//    public List<IssuesDTO> listIssues(IssuesQueryDTO issuesQueryDTO){
//        return feignExecute(() -> trackIssuesProvider.getIssuesList(issuesQueryDTO));
//    }

    @Override
    public void deleteIssues(Long id) {
       // feignExecute(() -> trackIssuesProvider.delete(id, null, null));
    }


}
