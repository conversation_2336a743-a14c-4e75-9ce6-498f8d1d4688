package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.DevopsSystemRepository;
import cn.harmonycloud.development.outbound.IssuesSubsystemRepository;
import cn.harmonycloud.development.outbound.SubsystemRepository;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.pojo.entity.DevopsSystem;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.issue.model.IssuesTypeDTO;
import cn.harmonycloud.issue.model.dto.v2.IssuesDTO;
import cn.harmonycloud.issue.provider.IssuesProvider;
import cn.harmonycloud.issue.provider.IssuesTypeProvider;
import cn.harmonycloud.tenant.TenantContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/3/14 8:35 下午
 **/
@Service
public class IssuesSubsystemRepositoryImpl implements IssuesSubsystemRepository, ApiRepository {

    @Autowired
    private IssuesTypeProvider issuesTypeProvider;
    @Autowired
    private IssuesProvider issuesProvider;
    @Value("${track-issues.subsystem.enable:false}")
    private Boolean enable;

    private Long issuesTypeId = null;

    @Value("${track-issues.subsystem.code:proj_app}")
    private String issuesTypeCode;

    @Autowired
    private SubsystemRepository subsystemRepository;
    @Autowired
    private DevopsSystemRepository systemRepository;

    @Override
    public Long createIssues(DevopsSubSystem subsystem) {
        if(!enable){
            return null;
        }
        IssuesDTO issuesDTO = new IssuesDTO();
    //    todo
//        issuesDTO.setIssuesTypeId(getIssuesTypeId());
//        issuesDTO.setCreateBy(subsystem.getCreateBy());
//        issuesDTO.setCreateTime(LocalDateTime.now());
//        issuesDTO.setUpdateBy(subsystem.getUpdateBy());
//        issuesDTO.setUpdateTime(LocalDateTime.now());
        Map customFields = new HashMap();
        DevopsSystem devopsSystem = systemRepository.getById(subsystem.getSystemId());
//        issuesDTO.setTitle(subsystem.getFullNameCn());
//        issuesDTO.setDescription(subsystem.getSubDescCn());
        customFields.put("system_id", subsystem.getSystemId());
        customFields.put("sys_code", devopsSystem.getSysCode());
        customFields.put("sub_system_id", subsystem.getId());
        customFields.put("sub_code", subsystem.getSubCode());
//        issuesDTO.setCustomFields(customFields);
        Long issuesId = feignExecute(() -> issuesProvider.addIssuesOfSys(issuesDTO, TenantContextHolder.getTenantId()));
        if(subsystem.getIssuesId() == null){
            DevopsSubSystem subsystemRecord = new DevopsSubSystem();
            subsystemRecord.setId(subsystem.getId());
            subsystemRecord.setIssuesId(issuesId);
            subsystemRepository.updateById(subsystemRecord);
        }
        return issuesId;
    }

    @Override
    public void deleteIssues(Long subsystemId) {
        DevopsSubSystem devopsSubsystem = subsystemRepository.getById(subsystemId);
        if(devopsSubsystem.getIssuesId() != null){
            feignExecute(() ->issuesProvider.delete(devopsSubsystem.getIssuesId(), null, null));
        }
    }

    private Long getIssuesTypeId(){
        if(issuesTypeId != null){
            return issuesTypeId;
        }
        IssuesTypeDTO issuesTypeDTO = feignExecute(() -> issuesTypeProvider.detailByCode(issuesTypeCode));
        return issuesTypeDTO.getId();
    }

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_ISSUES_FAIL;
    }
}
