package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.MajorVersionRepository;
import cn.harmonycloud.development.outbound.db.mapper.MajorVersionMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.MajorVersion;
import cn.harmonycloud.development.pojo.entity.VersionManagement;
import cn.harmonycloud.development.pojo.entity.VersionSubsystem;
import cn.harmonycloud.development.pojo.vo.version.MajorVersionQuery;
import cn.harmonycloud.development.pojo.vo.version.VersionQuery;
import cn.harmonycloud.development.pojo.vo.version.VersionSubsystemQuery;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class MajorVersionRepositoryImpl extends BaseRepositoryImpl<MajorVersionMapper, MajorVersion> implements MajorVersionRepository {
    @Override
    public List<MajorVersion> listByParams(Long subSystemId, String majorVersionNumber) {
        LambdaQueryWrapper<MajorVersion> query = new LambdaQueryWrapper<MajorVersion>();
        query.eq(MajorVersion::getSubSystemId,subSystemId);
        query.eq(StringUtils.isNotEmpty(majorVersionNumber),MajorVersion::getVersionNumber,majorVersionNumber);
        query.eq(MajorVersion::getDelFlag, 0);
        query.orderByDesc(MajorVersion::getCreateTime);
        return this.list(query);
    }

    @Override
    public Page<MajorVersion> pageQuery(MajorVersionQuery request) {
        Page<MajorVersion> page = new Page<>(request.getPageNo(), request.getPageSize());
        LambdaUpdateWrapper<MajorVersion> query = new LambdaUpdateWrapper<MajorVersion>();
        query.eq(MajorVersion::getDelFlag, SystemConstance.NOT_DELETE);
        query.eq(MajorVersion::getSubSystemId, request.getSubsystemId());
        query.eq(request.getVersionStatus() != null, MajorVersion::getVersionStatus, request.getVersionStatus());
        query.like(StringUtils.isNotEmpty(request.getVersionNumber()) , MajorVersion::getVersionNumber , request.getVersionNumber());
        query.orderByDesc(MajorVersion::getCreateTime);
        return this.page(page, query);
    }

    @Override
    public void deleteLogic(Long majorVersionId) {
        LambdaUpdateWrapper<MajorVersion> query = new LambdaUpdateWrapper<MajorVersion>();
        query.eq(MajorVersion::getId,majorVersionId);
        query.set(MajorVersion::getDelFlag,SystemConstance.IS_DELETE);
        this.update(query);
    }

    @Override
    public List<MajorVersion> listBySubSystemId(Long subSystemId) {
        LambdaUpdateWrapper<MajorVersion> query = new LambdaUpdateWrapper<MajorVersion>();
        query.eq(MajorVersion::getSubSystemId , subSystemId);
        query.eq(MajorVersion::getDelFlag , SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public List<MajorVersion> listByParams(List<Long> ids, List<Long> subsystemIds) {
        LambdaUpdateWrapper<MajorVersion> query = new LambdaUpdateWrapper<MajorVersion>();
        query.in(CollectionUtils.isNotEmpty(ids),MajorVersion::getId,ids);
        query.in(CollectionUtils.isNotEmpty(subsystemIds),MajorVersion::getSubSystemId,subsystemIds);
        query.eq(MajorVersion::getDelFlag,SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public List<MajorVersion> listByParams(Long subSystemId, Integer limit) {
        LambdaUpdateWrapper<MajorVersion> query = new LambdaUpdateWrapper<MajorVersion>();
        query.eq(MajorVersion::getSubSystemId, subSystemId);
        query.eq(MajorVersion::getDelFlag, SystemConstance.NOT_DELETE);
        query.orderByDesc(MajorVersion::getCreateTime);
        if (limit != null){
            query.last("limit " + limit);
        }
        return list(query);
    }

    @Override
    public boolean optimisticUpdates(Long id, String subVersionNumber, Long currentUserId, String oldSubNumber) {
        return this.update(new LambdaUpdateWrapper<MajorVersion>()
                .eq(MajorVersion::getId, id)
                .eq(MajorVersion::getSubNumber, oldSubNumber)
                .set(MajorVersion::getSubNumber, subVersionNumber)
                .set(MajorVersion::getUpdateBy, currentUserId)
                .set(MajorVersion::getUpdateTime, LocalDateTime.now()));
    }

    @Override
    public List<VersionSubsystem> versionSubsystemList(VersionSubsystemQuery query) {
        if (query.getIds() != null && query.getIds().size() == 0){
            return new ArrayList<>();
        }
        return this.baseMapper.versionSubsystemQuery(query);
    }
}
