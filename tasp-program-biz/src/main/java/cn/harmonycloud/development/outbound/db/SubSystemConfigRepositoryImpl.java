package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.SubSystemConfigRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsSubSystemConfigMapper;
import cn.harmonycloud.development.pojo.entity.SubSystemConfig;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/12 2:18 下午
 **/
@Service
public class SubSystemConfigRepositoryImpl extends BaseRepositoryImpl<DevopsSubSystemConfigMapper, SubSystemConfig> implements SubSystemConfigRepository {


    @Override
    public SubSystemConfig getByParams(Long subsystemId) {
        LambdaQueryWrapper<SubSystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubSystemConfig::getSubsystemId, subsystemId);
        return getOne(queryWrapper);
    }

    @Override
    public void removeBySubIds(List<Long> subIds) {
        if (CollectionUtils.isEmpty(subIds)) {
            return;
        }
        LambdaUpdateWrapper<SubSystemConfig> update = new LambdaUpdateWrapper<>();
        update.in(SubSystemConfig::getSubsystemId, subIds);
        this.remove(update);
    }

    @Override
    public List<SubSystemConfig> listByParams(int clearFlag) {
        LambdaQueryWrapper<SubSystemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubSystemConfig::getClearTemporaryBranchFlag, clearFlag);
        return list(queryWrapper);
    }
}
