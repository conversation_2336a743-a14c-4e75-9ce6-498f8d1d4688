package cn.harmonycloud.development.outbound.api.dto.coderepo;

import lombok.Data;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/7/24 11:09 上午
 **/
@Data
public class CodeCommit {

    private String authoredDate;
    private String authorEmail;
    private String authorName;
    private String committedDate;
    private String committerEmail;
    private String committerName;
    private String createdAt;
    private String id;
    private String message;
    private List<String> parentIds;
    private String shortId;
    private String status;
    private String timestamp;
    private String title;
    private String url;
    private String webUrl;

}
