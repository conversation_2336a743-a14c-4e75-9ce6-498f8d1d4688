package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.DeployEnvRepository;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DeployHostDTO;
import cn.harmonycloud.development.outbound.api.dto.pipeline.DoDeployEnv;
import cn.harmonycloud.development.outbound.api.dto.pipeline.SystemDeployEnvDTO;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.DeployEnvFeign;
import cn.harmonycloud.development.pojo.dto.operation.SystemEnv;
import cn.harmonycloud.enums.ExceptionCode;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/4/19 5:53 下午
 **/
@Service
public class DeployEnvRepositoryImpl implements DeployEnvRepository,ApiRepository {

    @Autowired
    private DeployEnvFeign deployEnvFeign;

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_PIPELINE_FAIL;
    }


    @Override
    public Map<Long, DoDeployEnv> envMap(List<Long> envIds) {
        if(CollectionUtils.isEmpty(envIds)){
            return new HashMap<>();
        }
        List<DoDeployEnv> envs = feignExecute(() -> deployEnvFeign.list(envIds));
        return envs.stream().collect(Collectors.toMap(DoDeployEnv::getId, env -> env));
    }

    @Override
    public Map<Long, DoDeployEnv> envMap() {
        Page<DoDeployEnv> page = feignExecute(() -> deployEnvFeign.page());
        if(CollectionUtils.isEmpty(page.getRecords())){
            return new HashMap<>();
        }
        return page.getRecords().stream().collect(Collectors.toMap(DoDeployEnv::getId, e ->e));
    }

    @Override
    public DoDeployEnv getById(Long envId) {
        if(envId == null){
            return null;
        }
        return envMap(Lists.newArrayList(envId)).get(envId);
    }

    @Override
    public List<SystemEnv> createSystemEnv(List<SystemEnv> systemEnvList) {
        return feignExecute(() -> deployEnvFeign.createSystemEnv(systemEnvList));
    }

    @Override
    public Map<Integer, DeployHostDTO> mapDeployHostByIds(List<Integer> hostIds) {
        if(CollectionUtils.isEmpty(hostIds)){
            return new HashMap<>();
        }
        List<DeployHostDTO> list = feignExecute(() -> deployEnvFeign.listHostByIds(hostIds));
        return list.stream().collect(Collectors.toMap(DeployHostDTO::getId, h -> h));
    }

    @Override
    public List<DoDeployEnv> listBySystem(Long systemId) {
        return feignExecute(() -> deployEnvFeign.listBySystemId(systemId));
    }
}
