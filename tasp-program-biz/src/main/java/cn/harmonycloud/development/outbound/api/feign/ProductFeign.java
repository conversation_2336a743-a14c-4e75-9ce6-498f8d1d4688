package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeDto;
import cn.harmonycloud.development.pojo.dto.product.ProductTreeRequestDto;
import cn.harmonycloud.development.config.CloudFeignConfiguration;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.pojo.dto.version.EnvironmentDTO;
import cn.harmonycloud.development.pojo.entity.PromotionInstanceDto;
import cn.harmonycloud.development.pojo.vo.repository.CreateInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ListInstanceReq;
import cn.harmonycloud.development.pojo.vo.repository.ProductPageQuery;
import cn.harmonycloud.development.pojo.vo.version.ProductPromotionVo;
import cn.harmonycloud.development.outbound.api.dto.promotion.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

@FeignClient(name = "product", url = "${cloud.product.url}",configuration = {FeignConfig.class, CloudFeignConfiguration.class})
public interface ProductFeign {

    /**
     * 获取制品或制品列表详细信息
     * @param p
     * @return
     */
    @PostMapping("/api/product/package/metadata")
    BaseResult<List<DetailDTO>> getProductPromotionDetail(@RequestBody PromotionDetailRequest p);


    /**
     * 上传制品
     * @param files
     * @param repositoryId
     * @param directory
     * @param assetFilename
     * @return
     */
    @PostMapping(value = "/api/product/upload",  consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    BaseResult upload(@RequestParam() MultipartFile[] files,
                      @RequestParam() Integer repositoryId,
                      @RequestParam() String directory,
                      @RequestParam() String assetFilename);


    /**
     * 下载制品
     * @param repositoryId
     * @param path
     * @return
     */
    @GetMapping(value = "api/product/download/{repositoryId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    BaseResult download(@PathVariable Integer repositoryId, @RequestBody String path);

    /**
     * 制品晋级
     * @param p
     * @return
     */
    @PostMapping("api/product/promotion")
    BaseResult promotion(@RequestBody ProductPromotionVo p);


    /**
     * 制品晋级环境查询
     * @return
     */
    @GetMapping("api/product/env")
    BaseResult<List<EnvironmentDTO>> getProductPromotionEnvironment();

    @PostMapping("/api/provider/product/updateMetadataByBuildId")
    BaseResult updateMetadataByBuildId(@RequestBody ProductMetadataDto req);

    /**
     * 分页查询制品接口
     * @param req
     * @return
     */
    @GetMapping("/api/provider/product/tree")
    BaseResult<List<ProductTreeDto>> tree(@SpringQueryMap ProductTreeRequestDto req);

    /**
     * 分页查询制品接口
     * @param repoIds
     * @return
     */
    @GetMapping("/api/provider/product/versionList")
    BaseResult<List<String>> versionList(@RequestParam(value= "systemId",required = false) Long systemId,
                                         @RequestParam(value= "subsystemIds",required = false) List<Long> subsystemIds,
                                         @RequestParam(value = "repoIds", required = false) List<Long> repoIds,
                                         @RequestParam(value = "kind", required = false) Integer kind);

    @GetMapping("/api/provider/product/listByVersion")
    BaseResult<List<cn.harmonycloud.development.pojo.dto.product.DevopsProductMetadataDto>> productList(@RequestParam Long subsystemId,
                                                                                                        @RequestParam String version,
                                                                                                        @RequestParam(required = false) String format,
                                                                                                        @RequestParam(required = false) Integer kind);

    @GetMapping("/api/product/list")
    public BaseResult<List<DevopsProductMetadataDto>> list(@RequestParam(value = "repoId", required = false) Long repoId,
                                                              @RequestParam(value = "subsystemId", required = false) Long subsystemId,
                                                              @RequestParam(value = "format", required = false) String format,
                                                              @RequestParam(value = "version", required = false) List<String> version,
                                                              @RequestParam(value = "path", required = false) String path);

    @GetMapping("/api/provider/product/list")
    BaseResult<List<DevopsProductMetadataDto>> list(@SpringQueryMap ProductPageQuery productPageQuery);

    @PostMapping("/api/promotion/createInstance")
    BaseResult<CreateInstanceRsp> createInstance(@RequestBody CreateInstanceReq createInstanceReq);

    @GetMapping("/api/promotion/listInstance")
    BaseResult<List<PromotionInstanceDto>> listPromotionInstance(@SpringQueryMap ListInstanceReq listInstanceReq);

    @GetMapping("/api/product/listByIds")
    BaseResult<List<DevopsProductMetadataDto>> listByIds(@RequestParam List<Long> ids);


    @GetMapping("/api/product/get")
    BaseResult<DevopsProductMetadataDto> get(@RequestParam(value = "repoId" ) Long repoId,
                                                    @RequestParam(value = "productName") String productName,
                                                    @RequestParam(value = "productVersion" ) String productVersion);
}
