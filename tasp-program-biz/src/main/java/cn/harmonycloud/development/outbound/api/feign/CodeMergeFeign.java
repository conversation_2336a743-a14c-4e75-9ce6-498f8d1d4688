package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.pojo.dto.scm.GetMergeStatisticInfoRequest;
import cn.harmonycloud.development.pojo.vo.scm.MyMergeRequestVO;
import cn.harmonycloud.development.pojo.vo.testenv.MergeRequestSettingVO;
import cn.harmonycloud.development.outbound.api.dto.scm.MergeRequestDTO;
import cn.harmonycloud.development.outbound.api.dto.scm.MergeTaskDto;
import cn.harmonycloud.development.outbound.api.dto.scm.MergeTaskRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.MergeTaskResponse;
import cn.harmonycloud.pojo.coderepo.MergeStatisticInfoResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "CodeMergeFeign", url = "${cloud.scm.url:http://devops-scm-service:8080}", configuration = {FeignConfig.class})
public interface CodeMergeFeign {

    /**
     * 子系统工作台-评审列表
     *
     * @param serverId
     * @param gitlabId
     * @param type     0-我创建的，1-我负责的
     * @return
     */
    @PostMapping("/{gitlabId}/merge/myMergeRequest/{type}")
    BaseResult<List<MyMergeRequestVO>> myMergeRequest(@RequestParam(name = "serverId", required = false) Long serverId,
                                                      @PathVariable(name = "gitlabId") Integer gitlabId,
                                                      @PathVariable(name = "type") Integer type);

    /**
     * 创建一个合并分组
     *
     * @param request
     * @return
     */
    @PostMapping("/mergeTask")
    BaseResult<MergeTaskResponse> mergeTask(@RequestBody MergeTaskRequest request);

    /**
     * 查询合并分组详情
     *
     * @param groupId
     * @return
     */
    @GetMapping("/mergeTask")
    BaseResult<MergeTaskDto> mergeTaskDetail(@RequestParam Long groupId);

    /**
     * 查询合并分组详情
     *
     * @param taskId
     * @return
     */
    @GetMapping("/mergeTask/task")
    BaseResult<MergeTaskDto> mergeTaskDetailByTaskId(@RequestParam Long taskId);

    /**
     * 冲突已解决（继续合并）
     *
     * @param branchId
     * @return
     */
    @PostMapping("/mergeTask/resolved/{branchId}")
    BaseResult proceedMerge(@PathVariable("branchId") Long branchId);

    /**
     * 取消合并
     *
     * @param taskId
     * @return
     */
    @PostMapping("/mergeTask/cancel")
    BaseResult mergeRequestCancel(@RequestParam("taskId") Long taskId);

    /**
     * 创建代码合并请求
     *
     * @param serverId        代码服务id
     * @param gitlabId        代码仓库id
     * @param mergeRequestDTO 合并请求参数
     * @return
     */
    @PostMapping("/{gitlabId}/merge/submitMergeRequest")
    BaseResult<Integer> submitRequestMerge(@RequestParam(name = "serverId") Long serverId,
                                           @PathVariable(name = "gitlabId") Integer gitlabId,
                                           @RequestBody MergeRequestDTO mergeRequestDTO);

    /**
     * 合并请求详情
     *
     * @param serverId       代码服务id
     * @param gitlabId       代码仓库id
     * @param mergeRequestId 合并请求iid
     * @return
     */
    @GetMapping("/{gitlabId}/merge/mergeRequestDetail/{mergeRequestId}")
    BaseResult<Object> requestMergeDetails(@RequestParam(name = "serverId") Long serverId,
                                           @PathVariable(name = "gitlabId") Integer gitlabId,
                                           @PathVariable(name = "mergeRequestId") Integer mergeRequestId);

    @GetMapping("/mergeRequestSetting/getByGitlabId/{gitlabId}")
    BaseResult<MergeRequestSettingVO> mergeSetting(@PathVariable Integer gitlabId);

    @PostMapping("/{gitlabId}/merge/closeMergeRequest")
    BaseResult merClose(@RequestParam(name = "serverId", required = false) Long serverId,
                        @PathVariable(name = "gitlabId") Integer gitlabId,
                        @RequestParam Integer iid);

    /**
     * 统计代码评审次数
     *
     * @param gitlabId
     * @return
     */
    @GetMapping("/dashboard/merge/count")
    BaseResult<Integer> mergeCount(@RequestParam("gitlabId") Integer gitlabId,
                                   @RequestParam("from") String ref,
                                   @RequestParam("to") String to);

    /**
     * 统计代码评审数量
     *
     * @param request
     * @return
     */
    @PostMapping("/merge/statisticInfo")
    BaseResult<List<MergeStatisticInfoResponse>> mergeStatistic(@RequestBody GetMergeStatisticInfoRequest request);
}
