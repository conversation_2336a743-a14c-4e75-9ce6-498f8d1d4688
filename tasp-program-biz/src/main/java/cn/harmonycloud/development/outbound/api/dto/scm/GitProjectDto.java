package cn.harmonycloud.development.outbound.api.dto.scm;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/9/2 4:24 下午
 **/
@Data
public class GitProjectDto implements Serializable {

    private static final long serialVersionUID = -9032820521485186500L;
    private Integer id;
    private String name;
    private String sshUrl;
    private String httpUrl; // 网关地址
    private Long serverId;
    private String internalUrl; // gitlab服务地址
    private String innerProxyHttp;
    private String description;
    private String path;
    private Integer starCount;
    private Integer forksCount;
    private Integer mergeRequestCount;
    private Integer issueCount;
    private String createdAt;
    private String defaultBranch;
    private List<GitProjectScanDTO> scanList;

}
