package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.FeatureBranchRepository;
import cn.harmonycloud.development.outbound.db.mapper.FeatureBranchMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.entity.FeatureBranch;
import cn.harmonycloud.development.pojo.vo.feature.FeatureBranchQuery;
import cn.harmonycloud.enums.FeatureStatusEnum;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/19 3:01 下午
 **/
@Service
public class FeatureBranchRepositoryImpl extends BaseRepositoryImpl<FeatureBranchMapper, FeatureBranch> implements FeatureBranchRepository {

    @Override
    public FeatureBranch getByParam(Long featureId) {
        LambdaQueryWrapper<FeatureBranch> query = new LambdaQueryWrapper<FeatureBranch>()
                .eq(FeatureBranch::getFeatureId, featureId)
                .eq(FeatureBranch::getDelFlag, SystemConstance.NOT_DELETE);
        List<FeatureBranch> featureBranches = this.list(query);
        return CollectionUtils.isEmpty(featureBranches) ? null : featureBranches.get(0);
    }

    @Override
    public List<FeatureBranch> listByParam(List<Long> featureIds) {
        if(CollectionUtils.isEmpty(featureIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FeatureBranch> query = new LambdaQueryWrapper<FeatureBranch>()
                .in(FeatureBranch::getFeatureId, featureIds)
                .eq(FeatureBranch::getDelFlag, SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public List<FeatureBranch> listBranchByQuery(FeatureBranchQuery query) {
        //查询，子系统id相同分支名称模糊查询，FeatureId、BranchName、BranchDevName在query包含的参数内，且分支未被删除
        LambdaQueryWrapper<FeatureBranch> q = new LambdaQueryWrapper<FeatureBranch>()
                .eq(query.getSubSystemId() != null, FeatureBranch::getSubSystemId, query.getSubSystemId())
                .like(StringUtils.isNotEmpty(query.getBranchName()), FeatureBranch::getBranchName, query.getBranchName())
                .in(CollectionUtils.isNotEmpty(query.getFeatureIds()),FeatureBranch::getFeatureId, query.getFeatureIds())
                .in(CollectionUtils.isNotEmpty(query.getBranchNames()),FeatureBranch::getBranchName, query.getBranchNames())
                .in(CollectionUtils.isNotEmpty(query.getBranchDevNames()),FeatureBranch::getBranchDevName, query.getBranchDevNames())
                .eq(FeatureBranch::getDelFlag, SystemConstance.NOT_DELETE);
        //返回FeatureBranch集合类型的查询结果
        return this.list(q);
    }

    @Override
    public List<FeatureBranch> listByParam(Long subsystemId) {
        LambdaQueryWrapper<FeatureBranch> query = new LambdaQueryWrapper<>();
        query.eq(FeatureBranch::getSubSystemId, subsystemId);
        query.eq(FeatureBranch::getDelFlag, SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public void removeByParams(List<Long> subsystemIds) {
        LambdaUpdateWrapper<FeatureBranch> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(FeatureBranch::getSubSystemId, subsystemIds);
        updateWrapper.set(FeatureBranch::getDelFlag, SystemConstance.IS_DELETE);
        this.update(updateWrapper);
    }

    @Override
    public Boolean removeById(Long id, Boolean logic) {
        if(logic){
            LambdaUpdateWrapper<FeatureBranch> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(FeatureBranch::getId, id);
            updateWrapper.set(FeatureBranch::getDelFlag, SystemConstance.IS_DELETE);
            return this.update(updateWrapper);
        }
        return this.removeById(id);
    }

    @Override
    public List<FeatureBranch> listByBranches(Long subsystemId, List<String> branches) {
        if(CollectionUtils.isEmpty(branches)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<FeatureBranch> query = new LambdaQueryWrapper<>();
        query.eq(FeatureBranch::getSubSystemId, subsystemId);
        query.and(lambda -> lambda.in(FeatureBranch::getBranchName, branches).or().in(FeatureBranch::getBranchDevName, branches));
        return list(query);
    }

    @Override
    public void clear(Long featureId, String clearCommit, String clearDevCommit, Long userId) {
        LambdaUpdateWrapper<FeatureBranch> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FeatureBranch::getFeatureId, featureId);
        updateWrapper.set(FeatureBranch::getClearCommit, clearCommit);
        updateWrapper.set(FeatureBranch::getClearDevCommit, clearDevCommit);
        updateWrapper.set(FeatureBranch::getIsClear, 1);
        this.update(updateWrapper);
    }
}
