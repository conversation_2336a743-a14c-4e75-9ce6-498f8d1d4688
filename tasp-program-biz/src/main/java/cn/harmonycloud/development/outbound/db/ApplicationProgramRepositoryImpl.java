package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.db.mapper.thgn.ApplicationProgramMapper;
import cn.harmonycloud.development.outbound.thgn.ApplicationProgramRepository;
import cn.harmonycloud.trinasolar.model.entity.ApplicationProgram;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import org.springframework.stereotype.Service;


/**
 * @Description
 * <AUTHOR> @Date 2025/03/25
 **/
@Service
public class ApplicationProgramRepositoryImpl extends BaseRepositoryImpl<ApplicationProgramMapper, ApplicationProgram> implements ApplicationProgramRepository {





}
