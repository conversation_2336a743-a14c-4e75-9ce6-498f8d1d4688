package cn.harmonycloud.development.outbound.api.dto.pipeline;

import com.alibaba.fastjson.JSONArray;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/24
 */
@Data
@ApiModel(value = "JobVO", description = "流水线视图对象")
public class JobDto {

    @ApiModelProperty(name = "jobId")
    private Long id;

    @ApiModelProperty(name = "job 名称")
    private String jobName;

    @ApiModelProperty(name = "job 状态")
    private String status;

    @ApiModelProperty(name = "job 最近一次执行时间")
    private String buildStart;

    @ApiModelProperty(name = "job 最近一次执行时间")
    private Long buildId;

    @ApiModelProperty(name = "标签")
    private List<String> label;

    @ApiModelProperty(name = "环境")
    private String envName;

    private String canReplay;

    @ApiModelProperty(name = "运行阶段")
    private JSONArray stages;

    private Long jobId;

    @ApiModelProperty(name = "状态")
    private String draftStatus;

    @ApiModelProperty(name = "启动变量")
    private List<JenkinsFileStartParameter> startParam;

}
