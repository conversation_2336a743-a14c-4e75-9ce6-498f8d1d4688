package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.constants.DevelopmentConstance;
import cn.harmonycloud.development.outbound.DevopsStageEnvRepository;
import cn.harmonycloud.development.outbound.db.mapper.DevopsStageEnvMapper;
import cn.harmonycloud.development.pojo.SystemConstance;
import cn.harmonycloud.development.pojo.dto.devopsstage.DevopsStageEnvListRequest;
import cn.harmonycloud.development.pojo.entity.DevopsStageEnv;
import cn.harmonycloud.development.service.mapstruct.DevopsStageMapstruct;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/2/10 11:01 上午
 **/
@Service
public class DevopsStageEnvRepositoryImpl extends BaseRepositoryImpl<DevopsStageEnvMapper, DevopsStageEnv> implements DevopsStageEnvRepository {

    @Autowired
    private DevopsStageMapstruct devopsStageMapstruct;

    @Override
    public List<DevopsStageEnv> envList(DevopsStageEnvListRequest request) {

        LambdaQueryWrapper<DevopsStageEnv> query = getDefaultQuery();
        query.eq(request.getStageId() != null, DevopsStageEnv::getStageId, request.getStageId());
        query.eq(request.getSubsystemId() != null, DevopsStageEnv::getSubsystemId, request.getSubsystemId());
        return Optional.ofNullable(list(query)).orElse(new ArrayList<>());
    }

    @Override
    public List<DevopsStageEnv> envList(Long subsystemId, Long stageId, String envName) {
        LambdaQueryWrapper<DevopsStageEnv> query = getDefaultQuery();
        query.eq(DevopsStageEnv::getSubsystemId, subsystemId);
        query.eq(DevopsStageEnv::getStageId, stageId);
        query.eq(DevopsStageEnv::getEnvName, envName);
        return Optional.ofNullable(list(query)).orElse(new ArrayList<>());
    }

    @Override
    public List<DevopsStageEnv> envList(Long subsystemId, String envCode) {
        LambdaQueryWrapper<DevopsStageEnv> query = getDefaultQuery();
        query.eq(DevopsStageEnv::getSubsystemId, subsystemId);
        query.eq(DevopsStageEnv::getStageEnvCode, envCode);
        return list(query);
    }

    @Override
    public boolean delete(Long stageEnvId, boolean logic, Long userId) {
        if(logic){
            return this.removeById(stageEnvId);
        }
        LambdaUpdateWrapper<DevopsStageEnv> update = new LambdaUpdateWrapper<>();
        update.eq(DevopsStageEnv::getId, stageEnvId);
        update.set(DevopsStageEnv::getDelFlag, 1);
        update.set(DevopsStageEnv::getUpdateBy, userId);
        update.set(DevopsStageEnv::getUpdateTime, LocalDateTime.now());
        return this.update(update);
    }

    @Override
    public Map<Integer,List<DevopsStageEnv>> envMap(List<Long> subSystemIds, Integer deployEnv, List<Integer> deployIds) {
        if (CollectionUtils.isEmpty(deployIds) || CollectionUtils.isEmpty(subSystemIds)){
            return new HashMap<>();
        }
        List<DevopsStageEnv> devopsStageEnvsAll = envList(subSystemIds);
        List<DevopsStageEnv> cluster = devopsStageEnvsAll.stream().filter(stageEnv -> {
            return DevelopmentConstance.EnvDeployType.k8s == stageEnv.getDeployType() && deployIds.contains(stageEnv.getClusterId());
        }).collect(Collectors.toList());
        List<DevopsStageEnv> host = devopsStageEnvsAll.stream().filter(stageEnv -> DevelopmentConstance.EnvDeployType.host == stageEnv.getDeployType() || DevelopmentConstance.EnvDeployType.virtually == stageEnv.getDeployType()).collect(Collectors.toList());
        Map<Long, DevopsStageEnv> mapHost = host.stream().collect(Collectors.toMap(DevopsStageEnv::getId, e -> e));
        Map<String, List<DevopsStageEnv>> singleHost = toSingleHostRaw(host);
        Map<Integer, List<DevopsStageEnv>> mapCluster = cluster.stream().filter(e -> e.getClusterId() != null).collect(Collectors.groupingBy(DevopsStageEnv::getClusterId));

        Map<Integer,List<DevopsStageEnv>> result = new HashMap<>();
        for (Integer deployId : deployIds) {
            List<DevopsStageEnv> devopsStageEnvs = mapCluster.get(deployId);
            if (CollectionUtils.isNotEmpty(devopsStageEnvs)){
                result.put(deployId, devopsStageEnvs);
                continue;
            }
            List<DevopsStageEnv> devopsStageEnvsHost = singleHost.getOrDefault(deployId.toString(), new ArrayList<>());
            for (DevopsStageEnv devopsStageEnv : devopsStageEnvsHost) {
                DevopsStageEnv e = mapHost.get(devopsStageEnv.getId());
                devopsStageEnv.setHostId(e.getHostId());
            }
            result.put(deployId, devopsStageEnvsHost);
        }
        return result;
    }

    @Override
    public List<DevopsStageEnv> envList(List<Long> subIds) {
        if(CollectionUtils.isEmpty(subIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<DevopsStageEnv> query = new LambdaQueryWrapper<>();
        query.in(DevopsStageEnv::getSubsystemId, subIds);
        query.eq(DevopsStageEnv::getDelFlag, SystemConstance.NOT_DELETE);
        return this.list(query);
    }

    @Override
    public void deleteByStageIds(List<Long> stageIds, Long userId) {
        LambdaUpdateWrapper<DevopsStageEnv> update = new LambdaUpdateWrapper<>();
        update.in(DevopsStageEnv::getStageId, stageIds);
        update.eq(DevopsStageEnv::getDelFlag, 0);
        update.set(DevopsStageEnv::getDelFlag, 1);
        update.set(DevopsStageEnv::getUpdateBy, userId);
        update.set(DevopsStageEnv::getUpdateTime, LocalDateTime.now());
        this.update(update);
    }

    /**
     * 将hostId字符数组转成单行形式
     * example : 1,2,3
     * to :      1
     *           2
     *           3
     *
     * @param list
     * @return Map<String, List<DevopsStageEnv>> 分组，单个hostId为key
     */
    private Map<String, List<DevopsStageEnv>> toSingleHostRaw(List<DevopsStageEnv> list){
        List<DevopsStageEnv> singleHost = new ArrayList<>();
        for (DevopsStageEnv devopsStageEnv : list) {
            if (StringUtils.isEmpty(devopsStageEnv.getHostId())){
                continue;
            }
            List<DevopsStageEnv> collect = Splitter.on(",").splitToStream(devopsStageEnv.getHostId()).map(hostId -> {
                DevopsStageEnv copy = devopsStageMapstruct.copy(devopsStageEnv);
                copy.setHostId(hostId);
                return copy;
            }).collect(Collectors.toList());
            singleHost.addAll(collect);
        }
        return singleHost.stream().collect(Collectors.groupingBy(DevopsStageEnv::getHostId));
    }

    /**
     * 环境列表
     *
     * @param deployType
     * @param deployIds    deployType = DevelopmentConstance.EnvDeployType.k8s 时生效
     * @return
     */
    private List<DevopsStageEnv> deployList(Integer deployType, List<Integer> deployIds){
        LambdaQueryWrapper<DevopsStageEnv> query = new LambdaQueryWrapper<>();
        query.eq(DevopsStageEnv::getDeployType, deployType);
        query.eq(DevopsStageEnv::getDelFlag, SystemConstance.NOT_DELETE);
        query.in(deployType == DevelopmentConstance.EnvDeployType.k8s && CollectionUtils.isNotEmpty(deployIds), DevopsStageEnv::getClusterId, deployIds);
        return list(query);
    }

    private LambdaQueryWrapper<DevopsStageEnv> getDefaultQuery(){
        LambdaQueryWrapper<DevopsStageEnv> query = new LambdaQueryWrapper<>();
        query.eq(DevopsStageEnv::getDelFlag, 0);
        return query;
    }


}
