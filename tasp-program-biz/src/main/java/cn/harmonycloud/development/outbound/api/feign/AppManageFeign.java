package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.resourceinstance.ResourceInstancesDto;
import cn.harmonycloud.development.outbound.api.dto.role.RoleBindingDto;
import cn.harmonycloud.development.outbound.api.dto.role.RoleInfoDto;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.development.config.CloudFeignConfiguration;
import cn.harmonycloud.development.config.CloudFeignException;
import cn.harmonycloud.development.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@CloudFeignException(exceptionCode = ExceptionCode.REMOTE_APP_MANAGE_FAIL)
@FeignClient(name = "appManageService", url = "${amp.url}",configuration = {FeignConfig.class, CloudFeignConfiguration.class})
public interface AppManageFeign {

    /**
     * 应用管理资源相关
     * 通过应用管理的底层基础能力来实现权限
     * 包括查询人员权限、角色信息等一些列接口
     */

    // 注册资源实例接口
    @PostMapping("/provider/resourceInstances/wrapper")
    BaseResult<Boolean> resourceInstances(@RequestBody ResourceInstancesDto resourceInstancesDto);

    // 查询资源下的角色
    @GetMapping("/provider/roles/roleTypeCode")
    BaseResult<List<RoleInfoDto>> roles(@RequestParam("reosurceTypeCode") String resourceTypeCode);

    // 获取用户资源转换后的用户角色对象
    @GetMapping("/provider/appRole/source")
    BaseResult<RoleBindingDto> appRoleUser(@RequestParam String sourceResourceCode,
                                           @RequestParam String sourceRoleId,
                                           @RequestParam String targetAppCode,
                                           @RequestParam String targetResourceCode);

    // 获取用户资源转换后的用户角色对象
    @GetMapping("/provider/appRole/list")
    BaseResult<List<RoleBindingDto>> listRoleUser(@RequestParam String sourceResourceCode,
                                           @RequestParam(required = false) String sourceRoleId,
                                           @RequestParam String targetAppCode,
                                           @RequestParam String targetResourceCode);


    @GetMapping("/provider/permissions/resources/map")
    BaseResult<Map<Long, Map<String, Boolean>>> mapPermission(@RequestParam String resourceTypeCode,
                                                              @RequestParam List<Long> resourceInstanceIds);

}
