package cn.harmonycloud.development.outbound.db;

import cn.harmonycloud.development.outbound.FeatureStarRepository;
import cn.harmonycloud.development.outbound.db.mapper.FeatureStarMapper;
import cn.harmonycloud.development.pojo.entity.FeatureStar;
import cn.harmonycloud.mybatis.base.BaseRepositoryImpl;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023/5/9 5:10 下午
 **/
@Service
public class FeatureStarRepositoryImpl extends BaseRepositoryImpl<FeatureStarMapper, FeatureStar> implements FeatureStarRepository {


}
