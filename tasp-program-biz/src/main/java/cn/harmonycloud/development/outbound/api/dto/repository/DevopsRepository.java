package cn.harmonycloud.development.outbound.api.dto.repository;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: DevopsRepository
 * @Description
 * @Author: wangliang
 * @Date: 2022/03/14
 **/
@Data
public class DevopsRepository {

    @Schema(description = "制品库id")
    private Long id;

    @Schema(description = "制品库名称")
    private String name;

    @Schema(description = "制品库格式")
    private String format;

    @Schema(description = "制品库类型", example = "harbor,nexus")
    private String type;

    @Schema(description = "制品库储存类型", example = "hosted,group,proxy")
    private String repositoryType;

    @Schema(description = "制品库种类，0->组件库，1->依赖库 2->制品库  3->生产前置库")
    private Integer kind;

    @Schema(description = "权限范围0公开，1项目私有", example = "0公开，1项目私有")
    private Integer scope;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "策略类型，maven-hosted类型：RELEASE  SNAPSHOTS")
    private String versionPolicy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "存储容量")
    private Integer storageLimit;

    @TableField(exist = false)
    private String writePolicy;

    @TableField(exist = false)
    private String url;

    @Schema(description = "项目id")
    private Integer projectId;

    @Schema(description = "子系统id")
    private Long subSystemId;

    @Schema(description = "组织id")
    private String tenantId;

    @Schema(description = "环境id")
    private Integer envConfigId;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 子系统code
     */
    private String subSystemCode;

    private Long envId;
    /**
     * 系统code
     */
    private String systemCode;

    @TableField(exist = false)
    private Boolean online;

    @TableField(exist = false)
    private List<String> groups;

    @Schema(description = "组件库类型，0公共，1系统，2其他")
    private String pluginType;
}