package cn.harmonycloud.development.outbound.api.enums;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/3/17
 */
@Getter
@AllArgsConstructor
public enum LanguageEnum {
    css("CSS" , "CSS"),
    scala("Scala" , "Scala"),
    jsp("JSP" , "JSP"),
    go("Go" , "Go"),
    kotlin("<PERSON>tl<PERSON>" , "Kotlin"),
    py("Python" , "Python"),
    js("JavaScript" , "JavaScript"),
    ruby("Ruby"  , "Ruby"),
    cs("C#" ,"C#"),
    java("Java" , "Java"),
    web("HTML" , "HTML"),
    flex("Flex" , "Flex"),
    xml("XML" , "XML"),
    php("PHP" , "PHP"),
    json("JSON" , "JSON"),
    terraform("Terraform" , "Terraform"),
    text("Text" , "Text"),
    vbnet("VB.NET" , "VB.NET"),
    cloudformation("CloudFormation" , "CloudFormation"),
    ts("TypeScript" , "TypeScript"),
    cxx("CXX" , "CXX"),
    swift("Swift" , "Swift"),
    objc("Objective-C" , "Objective-C"),
    yaml("YAML" , "YAML"),
    vue("VUE" , "JavaScript"),
    react("REACT" , "JavaScript"),
    其他("其他" , "其他");

    private String languageCN;

    private String scanType;

    @Data
    @AllArgsConstructor
    public static class LanguageVO {
        private String language;
        private String languageCN;
    }

    public static List<LanguageVO> toVO() {
        return Arrays.stream(LanguageEnum.values()).map(lan -> new LanguageVO(lan.name(), lan.getLanguageCN())).collect(Collectors.toList());
    }

    public static String getLanguageByCn(String languageCN){
        for (LanguageEnum value : LanguageEnum.values()) {
            if(value.languageCN.equals(languageCN)){
                return value.name();
            }
        }
        return null;
    }
    public static String getScanType(String languageCN){
        for (LanguageEnum value : LanguageEnum.values()) {
            if (value.languageCN.equals(languageCN)){
                return value.getScanType();
            }
        }
        return null;
    }
}