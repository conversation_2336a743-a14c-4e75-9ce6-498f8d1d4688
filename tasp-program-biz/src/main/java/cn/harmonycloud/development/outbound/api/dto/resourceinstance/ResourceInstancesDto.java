package cn.harmonycloud.development.outbound.api.dto.resourceinstance;

import lombok.Data;

/**
 * @Description 注册资源实例传输对象
 * <AUTHOR>
 * @Date 2022/9/19 5:29 下午
 **/
@Data
public class ResourceInstancesDto {

    // 备注
    private String description;
    // 拓展字段
    private String extendField;
    // 父级应用资源id
    private String parentResourceInstanceId;
    // 父级资源类型编号
    private String parentResourceTypeCode;
    // 应用资源id
    private Long resourceInstanceId;
    // 应用资源名称
    private String resourceInstanceName;
    // 赛选表，非必填
    private Object[] resourceScreenings;
    // 资源类型编号
    private String resourceTypeCode;

}
