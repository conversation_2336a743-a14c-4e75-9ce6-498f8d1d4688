package cn.harmonycloud.development.outbound.api;

import cn.harmonycloud.development.outbound.CodeMergeRepository;
import cn.harmonycloud.development.outbound.api.execute.ApiRepository;
import cn.harmonycloud.development.outbound.api.feign.CodeMergeFeign;
import cn.harmonycloud.development.outbound.api.feign.CodeProjectFeign;
import cn.harmonycloud.development.pojo.dto.scm.GetMergeStatisticInfoRequest;
import cn.harmonycloud.development.pojo.vo.scm.MyMergeRequestVO;
import cn.harmonycloud.development.pojo.vo.testenv.MergeRequestSettingVO;
import cn.harmonycloud.development.outbound.api.dto.scm.*;
import cn.harmonycloud.enums.ExceptionCode;
import cn.harmonycloud.pojo.coderepo.MergeStatisticInfoResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class CodeMergeRepositoryImpl implements CodeMergeRepository, ApiRepository {

    @Autowired
    private CodeMergeFeign codeMergeFeign;
    @Autowired
    private CodeProjectFeign codeProjectFeign;

    @Override
    public ExceptionCode getException() {
        return ExceptionCode.REMOTE_SCM_FAIL;
    }

    @Override
    public MergeTaskResponse mergeTask(MergeTaskRequest request) {
        return feignExecute(() -> codeMergeFeign.mergeTask(request));
    }

    @Override
    public Integer mergeCount(Integer gitlabId, String from, String to) {
        return feignExecute(() -> codeMergeFeign.mergeCount(gitlabId, from, to));
    }

    @Override
    public MergeRequestSettingVO mergeSetting(Integer gitlabId) {
        return feignExecute(() -> codeMergeFeign.mergeSetting(gitlabId));
    }

    @Override
    public void merClose(Integer gitlabId, Integer mergeIid) {
        GitProjectDto gitProject = feignExecute(() -> codeProjectFeign.project(gitlabId));
        feignExecute(() -> codeMergeFeign.merClose(gitProject.getServerId(), gitlabId, mergeIid));
    }

    @Override
    public List<MyMergeRequestVO> myMergeRequest(int gitlabId, Integer type) {
        return feignExecute(()-> codeMergeFeign.myMergeRequest(null, gitlabId, type));
    }

    @Override
    public List<MergeStatisticInfoResponse> mergeStatistic(List<Integer> gitlabIds) {
        if(CollectionUtils.isEmpty(gitlabIds)){
            return new ArrayList<>();
        }
        GetMergeStatisticInfoRequest request = new GetMergeStatisticInfoRequest();
        request.setGitlabIds(gitlabIds);
        return feignExecute(()-> codeMergeFeign.mergeStatistic(request));
    }

    @Override
    public MergeTaskDto getMergeTask(Long mergeGroupId) {
        return feignExecute(() -> codeMergeFeign.mergeTaskDetail(mergeGroupId));
    }

    @Override
    public MergeTaskDto getMergeTaskByTaskId(Long taskId) {
        return feignExecute(() -> codeMergeFeign.mergeTaskDetailByTaskId(taskId));
    }

    @Override
    public Integer submitRequestMerge(Integer gitlabId, MergeRequestDTO requestDTO) {
        GitProjectDto gitProject = feignExecute(() -> codeProjectFeign.project(gitlabId));
        return feignExecute(() -> codeMergeFeign.submitRequestMerge(gitProject.getServerId(), gitlabId, requestDTO));
    }

    @Override
    public Object requestMergeDetails(Integer gitlabId, Integer mergeIid) {
        GitProjectDto gitProject = feignExecute(() -> codeProjectFeign.project(gitlabId));
        return feignExecute(() -> codeMergeFeign.requestMergeDetails(gitProject.getServerId(), gitlabId, mergeIid));
    }

    @Override
    public void proceedMergeTask(Long mergeTaskBranchId) {
        feignExecute(() -> codeMergeFeign.proceedMerge(mergeTaskBranchId));
    }

    @Override
    public void mergeRequestCancel(Long mergeTaskId) {
        feignExecute(() -> codeMergeFeign.mergeRequestCancel(mergeTaskId));
    }
}
