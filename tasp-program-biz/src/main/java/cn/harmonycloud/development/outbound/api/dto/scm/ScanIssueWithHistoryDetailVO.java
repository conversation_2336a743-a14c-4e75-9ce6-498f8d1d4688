package cn.harmonycloud.development.outbound.api.dto.scm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName ScanIssueWithHistoryDetailVO
 * @Description
 * <AUTHOR>
 * @Date 2023/2/23 2:04 PM
 **/
@Data
@ApiModel(value = "ScanIssueWithHistoryDetailVO", description = "代码扫描页面封装视图对象")
public class ScanIssueWithHistoryDetailVO {

    private Long serverId;
    private Integer gitlabId;

    @ApiModelProperty("问题列表")
    private Page<ScanIssueVO> issuePage = new Page<>();

    @ApiModelProperty("扫描详情")
    private HistoryDetailVO history = new HistoryDetailVO();

    @Data
    public static class HistoryDetailVO{
        private Long id;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String commitId;
        private String scanMsg;
        private int status;

        private int total;
        private int blocker;
        private int critical;
        private int major;
        private int minor;

        /**
         * 手动设置的门禁阈值
         */
        private int blockerError;
        private int criticalError;
        private int majorError;
        private int minorError;

        private String blockerIsPass;
        private String criticalIsPass;
        private String majorIsPass;
        private String minorIsPass;

        /**
         * 0-扫描中 1-扫描通过 2-扫描不通过 3-扫描异常
         */
        private Integer totalIsPassCode;
        private String totalIsPass;
    }


}
