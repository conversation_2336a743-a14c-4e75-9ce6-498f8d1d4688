package cn.harmonycloud.development.outbound.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 制品格式
 *
 * <AUTHOR>
 * @Date 2022/3/15
 **/
@Getter
@AllArgsConstructor
public enum RepositoryFormatEnum {

    /**
     * 通用制品
     */
    RAW(RepositoryConst.NEXUS, "raw", "raw", "Raw"),
    /**
     * maven2类型制品
     */
    MAVEN(RepositoryConst.NEXUS, "maven2", "maven", "Maven"),
    /**
     * npm类型制品
     */
    NPM(RepositoryConst.NEXUS, "npm", "npm", "Npm"),
    /**
     * nuget类型制品
     */
    NUGET(RepositoryConst.NEXUS, "nuget", "nuget", "Nuget"),
    /**
     * composer类型制品
     */
    COMPOSER(RepositoryConst.NEXUS, "composer", "composer", "Composer"),
    /**
     * docker类型制品
     */
    DOCKER(RepositoryConst.HARBOR, "docker", "docker", "Docker"),
    /**
     * helm制品
     */
    HELM(RepositoryConst.HARBOR, "helm", "helm", "Helm"),
    /**
     * go制品
     */
    GO(RepositoryConst.NEXUS, "go", "go", "Go"),
    /**
     * python制品
     */
    PYPI(RepositoryConst.NEXUS, "pypi", "pypi", "Pypi"),
    ;

    /**
     * 存储位置
     */
    private final String repository;

    /**
     * 格式
     */
    private final String format;

    /**
     * 别名
     */
    private final String alias;

    /**
     * 大写首字母名字
     */
    private final String capitalLetter;

    public static String getRepositoryByFormat(String format) {
        for (RepositoryFormatEnum repositoryFormatEnum : RepositoryFormatEnum.values()) {
            if (repositoryFormatEnum.getFormat().equals(format)
                    || repositoryFormatEnum.getAlias().equals(format)) {
                return repositoryFormatEnum.getRepository();
            }
        }
        return null;
    }

    public static RepositoryFormatEnum getByFormat(String format) {
        for (RepositoryFormatEnum repositoryFormatEnum : RepositoryFormatEnum.values()) {
            if (repositoryFormatEnum.getFormat().equals(format)
                    || repositoryFormatEnum.getAlias().equals(format)) {
                return repositoryFormatEnum;
            }
        }
        return null;
    }
}
