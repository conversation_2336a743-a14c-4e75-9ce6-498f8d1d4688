package cn.harmonycloud.development.outbound.api.feign;

import cn.harmonycloud.common.core.base.BaseResult;
import cn.harmonycloud.development.outbound.api.dto.repository.DevopsRepository;
import cn.harmonycloud.development.outbound.api.dto.repository.PromotionStrategyDTO;
import cn.harmonycloud.development.outbound.api.dto.repository.UnBindRepository;
import cn.harmonycloud.development.pojo.dto.repository.SystemRepoQuery;
import cn.harmonycloud.development.config.CloudFeignConfiguration;
import cn.harmonycloud.development.config.FeignConfig;
import cn.harmonycloud.development.outbound.api.dto.promotion.*;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "repository", url = "${cloud.product.url}",configuration = {FeignConfig.class, CloudFeignConfiguration.class})
public interface RepositoryFeign {



    /**
     * 创建制品仓库
     * @param repositoryDto
     * @return
     */
    @PostMapping("/api/repository")
    BaseResult<DevopsRepository> createRepository(@RequestBody RepositoryDto repositoryDto);

    /**
     * 查询所有制品库
     * @param query
     * @return
     */
    @GetMapping("/api/provider/repository/listAll")
    BaseResult<List<DevopsRepository>> listAll(@SpringQueryMap SystemRepoQuery query);


    /**
     * 保存系统harbor库
     * @param systemCode
     * @return
     */
    @PostMapping("/api/provider/saveSystemHarbor")
    BaseResult saveSystemHarborRepo(@RequestParam String systemCode);

    @PostMapping("/api/provider/repository/remove")
    BaseResult  delete(@RequestParam Long id);

    /**
     * 晋级策略列表
     *
     * @param systemId
     * @param format
     * @return
     */
    @GetMapping("/api/promotion/listStrategyBySystem")
    public BaseResult<List<PromotionStrategyDTO>> listStrategyBySystem(@RequestParam Long systemId, @RequestParam(required = false) String format);

    /**
     * 晋级策略列表
     *
     * @param ids
     * @return
     */
    @GetMapping("/api/repository/listByIds")
    public BaseResult<List<DevopsRepository>> listByIds(@RequestParam(value = "ids") List<Long> ids);

    /**
     * 晋级策略列表
     *
     * @param unBindRepository
     * @return
     */
    @PostMapping("/api/provider/repository/unbind")
    BaseResult unbind(@RequestBody UnBindRepository unBindRepository);
}
