package cn.harmonycloud.development.outbound.api.dto.repository;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/4/18 4:46 下午
 **/
@Data
public class PromotionNodeDTO {

    private Long id;
    /**
     * 策略id
     */
    private Long strategyId;
    /**
     * 名称
     */
    private Long repoId;

    /**
     * 名称
     */
    private String repoName;

    private Long createBy;

    private LocalDateTime createTime;

}
