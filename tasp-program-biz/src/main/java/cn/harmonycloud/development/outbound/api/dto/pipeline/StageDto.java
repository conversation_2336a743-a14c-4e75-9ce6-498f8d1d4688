package cn.harmonycloud.development.outbound.api.dto.pipeline;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/1/25
 */
@Data
@ApiModel(value = "StageVO", description = "流水线步骤")
public class StageDto {

    @ApiModelProperty("构建步骤id")
    private String id;

    @ApiModelProperty("构建步骤名字")
    private String name;

    @ApiModelProperty("卡点确定人")
    private String confirmationUser;

    @ApiModelProperty("构建步骤结果")
    private String status;

    @ApiModelProperty("开始时间(ms)")
    private Long startTimeMillis;

    @ApiModelProperty("stage的日志")
    private String text;

    @ApiModelProperty("构建步骤耗时(ms)")
    private Long durationMillis;

    @ApiModelProperty("并行步骤")
    private List<StageDto> parallel;

    @ApiModelProperty("step步骤")
    private List<String> steps;

    @ApiModelProperty("错误信息")
    private String errMsg;

    @ApiModelProperty("错误类型")
    private String errType;

}
