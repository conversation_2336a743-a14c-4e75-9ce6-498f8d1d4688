package cn.harmonycloud.development.outbound.api.dto.scm;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/8/24 10:41 上午
 **/
@Data
public class MergeTaskBranchDto implements Serializable {

    private static final long serialVersionUID = 3815157698276794904L;
    @ApiModelProperty("分支名")
    private String name;
    @ApiModelProperty("commitId")
    private String commitId;
    @ApiModelProperty("状态：NOT_MERGED,MERGED,CONFLICT")
    private String status;

    private Long id;
}
