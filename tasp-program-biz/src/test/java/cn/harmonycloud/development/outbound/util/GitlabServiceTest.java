package cn.harmonycloud.development.outbound.util;

import cn.harmonycloud.development.execption.thgn.GitException;
import cn.harmonycloud.development.service.trinasolar.GitlabService;
import cn.harmonycloud.enums.ExceptionCode;
import org.gitlab4j.api.GitLabApi;
import org.gitlab4j.api.GitLabApiException;
import org.gitlab4j.api.ProjectApi;
import org.gitlab4j.api.models.Project;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * GitlabUtil单元测试类
 *
 * <AUTHOR>
 */
@RunWith(MockitoJUnitRunner.class)
public class GitlabServiceTest {

    @InjectMocks
    private GitlabService gitlabService;

    @Mock
    private GitLabApi mockGitLabApi;

    @Mock
    private ProjectApi mockProjectApi;

    @Mock
    private Project mockProject;

    /**
     * 测试前的初始化方法
     * 创建GitlabUtil实例并设置必要的配置参数
     */
    @Before
    public void setUp() {
        // 创建被测试的GitlabUtil实例
        gitlabService = new GitlabService();

        // 使用反射设置私有字段的测试值，模拟Spring配置注入
        ReflectionTestUtils.setField(gitlabService, "gitUrl", "https://code.trinasolar.com");
        ReflectionTestUtils.setField(gitlabService, "gitToken", "**************************");
    }

    /**
     * 测试createGitlabRepository方法的成功场景
     *
     * 测试目标：验证在正常情况下能够成功创建GitLab仓库
     * 测试步骤：
     * 1. 准备测试数据（仓库URL）
     * 2. Mock GitLabApi和ProjectApi的行为
     * 3. 调用被测试方法
     * 4. 验证返回结果和方法调用
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_Success() throws GitLabApiException {
        // Given - 准备测试数据
        String repoUrl = "https://code.trinasolar.com/itid/test186/test-prog-1866.git";

        // 使用spy来部分mock GitlabUtil，保留真实的createGitlabRepository方法逻辑
        GitlabService spyGitlabService = spy(gitlabService);
        // Mock getGitLabApi方法返回我们的mock对象
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：返回mock的ProjectApi
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        // Mock ProjectApi的createProject方法：返回mock的Project对象
        when(mockProjectApi.createProject(repoUrl)).thenReturn(mockProject);

        // When - 执行被测试的方法
        Project result = spyGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNotNull("返回的Project对象不应该为null", result);
      //  assertEquals("返回的Project对象应该与mock对象相同", mockProject, result);
        // 验证ProjectApi的createProject方法被正确调用
//        verify(mockProjectApi).createProject(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法的异常处理场景
     *
     * 测试目标：验证当GitLab API调用失败时，方法能够正确处理异常并转换为业务异常
     * 测试步骤：
     * 1. 准备测试数据和异常对象
     * 2. Mock GitLabApi抛出GitLabApiException
     * 3. 调用被测试方法
     * 4. 验证抛出的GitException包含正确的错误代码和消息
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_GitLabApiException() throws GitLabApiException {
        // Given - 准备测试数据和异常
        String repoUrl = "test-repo";
        String errorMessage = "API调用失败";
        GitLabApiException gitLabApiException = new GitLabApiException(errorMessage);

        // 使用spy来部分mock GitlabUtil
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：ProjectApi的createProject方法抛出异常
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenThrow(gitLabApiException);

        // When & Then - 执行方法并验证异常
        try {
            spyGitlabService.createGitlabRepository(repoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL",
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'仓库创建失败'",
                      exception.getMessage().contains("仓库创建失败"));
            assertTrue("异常消息应该包含原始错误信息",
                      exception.getMessage().contains(errorMessage));
        }

        // 验证ProjectApi的createProject方法被调用
        verify(mockProjectApi).createProject(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法处理null参数的场景
     *
     * 测试目标：验证当传入null仓库URL时，方法能够正确处理并抛出适当的异常
     * 测试步骤：
     * 1. 准备null参数
     * 2. Mock GitLabApi在接收到null参数时抛出异常
     * 3. 调用被测试方法
     * 4. 验证抛出的GitException包含正确的错误代码
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_NullRepoUrl() throws GitLabApiException {
        // Given - 准备null参数
        String repoUrl = null;

        // 使用spy来部分mock GitlabUtil
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：当传入null参数时抛出异常
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl))
            .thenThrow(new GitLabApiException("Invalid repository URL"));

        // When & Then - 执行方法并验证异常
        try {
            spyGitlabService.createGitlabRepository(repoUrl);
            fail("应该抛出GitException，但方法正常执行完成");
        } catch (GitException exception) {
            // 验证异常的错误代码
            assertEquals("异常代码应该是CREATE_REPOSITORY_FAIL",
                        ExceptionCode.CREATE_REPOSITORY_FAIL, exception.getExceptionCode());
            // 验证异常消息包含预期的错误信息
            assertTrue("异常消息应该包含'仓库创建失败'",
                      exception.getMessage().contains("仓库创建失败"));
        }

        // 验证ProjectApi的createProject方法被调用（即使参数为null）
        verify(mockProjectApi).createProject(repoUrl);
    }

    /**
     * 测试createGitlabRepository方法处理API返回null的场景
     *
     * 测试目标：验证当GitLab API返回null时，方法能够正确处理并返回null
     * 测试步骤：
     * 1. 准备有效的仓库URL
     * 2. Mock GitLabApi的createProject方法返回null
     * 3. 调用被测试方法
     * 4. 验证方法返回null且没有抛出异常
     *
     * @throws GitLabApiException 如果GitLab API调用失败
     */
    @Test
    public void testCreateGitlabRepository_ProjectApiReturnsNull() throws GitLabApiException {
        // Given - 准备测试数据
        String repoUrl = "test-repo";

        // 使用spy来部分mock GitlabUtil
        GitlabService spyGitlabService = spy(gitlabService);
        doReturn(mockGitLabApi).when(spyGitlabService).getGitLabApi();

        // Mock GitLabApi的行为：createProject方法返回null
        when(mockGitLabApi.getProjectApi()).thenReturn(mockProjectApi);
        when(mockProjectApi.createProject(repoUrl)).thenReturn(null);

        // When - 执行被测试的方法
        Project result = spyGitlabService.createGitlabRepository(repoUrl);

        // Then - 验证结果
        assertNull("当API返回null时，方法也应该返回null", result);
        // 验证ProjectApi的createProject方法被正确调用
        verify(mockProjectApi).createProject(repoUrl);
    }

    /**
     * 测试getGitLabApi方法的基本功能
     *
     * 测试目标：验证getGitLabApi方法能够正确创建并返回GitLabApi实例
     * 测试步骤：
     * 1. 调用getGitLabApi方法
     * 2. 验证返回的GitLabApi实例不为null
     *
     * 注意：这个测试会创建真实的GitLabApi实例，需要有效的URL配置
     */
    @Test
    public void testGetGitLabApi() {
        // When - 执行被测试的方法
        GitLabApi result = gitlabService.getGitLabApi();

        // Then - 验证结果
        assertNotNull("getGitLabApi方法应该返回非null的GitLabApi实例", result);

        // 验证GitLabApi实例的基本属性
        // 注意：由于GitLabApi是第三方库，我们主要验证实例创建成功
    }
}
