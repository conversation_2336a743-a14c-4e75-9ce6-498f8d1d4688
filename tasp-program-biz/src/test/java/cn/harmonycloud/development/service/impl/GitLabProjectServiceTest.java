package cn.harmonycloud.development.service.impl;

import cn.harmonycloud.development.outbound.CodeProjectRepository;
import cn.harmonycloud.development.outbound.api.dto.scm.ProjectRequest;
import cn.harmonycloud.development.outbound.api.dto.scm.ProjectResponse;
import cn.harmonycloud.development.pojo.entity.DevopsSubSystem;
import cn.harmonycloud.development.service.SystemComponentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * GitLabProjectService 测试类
 * 主要测试 GitLab 项目创建时的命名规范修复
 */
@ExtendWith(MockitoExtension.class)
class GitLabProjectServiceTest {

    @Mock
    private SystemComponentService systemComponentService;

    @Mock
    private CodeProjectRepository codeProjectRepository;

    @InjectMocks
    private GitLabProjectService ********************;

    private DevopsSubSystem mockSubSystem;
    private ProjectResponse mockProjectResponse;

    @BeforeEach
    void setUp() {
        // 创建模拟的子系统对象
        mockSubSystem = new DevopsSubSystem();
        mockSubSystem.setId(1L);
        mockSubSystem.setSystemId(100L);
        mockSubSystem.setFullNameCn("测试应用中文名称"); // 包含中文字符
        mockSubSystem.setSubCode("test-app-code"); // 符合 GitLab 命名规范
        mockSubSystem.setSubDescCn("测试应用描述");
        mockSubSystem.setTechnology("JAVA");

        // 创建模拟的项目响应对象
        mockProjectResponse = new ProjectResponse();
        mockProjectResponse.setId(123L);
    }

    @Test
    void testCreateProject_ShouldUseSubCodeAsProjectName() {
        // Given
        Long subSystemId = 1L;
        String groupId = "10";
        
       // when(subsystemRepository.getById(subSystemId)).thenReturn(mockSubSystem);
        when(systemComponentService.getComponentKeyBySystemId(mockSubSystem.getSystemId())).thenReturn(groupId);
        when(codeProjectRepository.createProject(any(ProjectRequest.class))).thenReturn(mockProjectResponse);

        // When
        Long result = ********************.createProject(subSystemId, new ProjectRequest(), false, false);

        // Then
        assertEquals(123L, result);
        
        // 验证 ProjectRequest 的设置
        verify(codeProjectRepository).createProject(argThat(request -> {
            // 验证使用 subCode 而不是 fullNameCn 作为项目名称
            assertEquals("test-app-code", request.getName());
            assertEquals("test-app-code", request.getPath());
            assertEquals(groupId, request.getGroupId());
            assertEquals("测试应用描述", request.getDescription());
            assertEquals(true, request.getInitializeWithReadme());
            return true;
        }));
    }

    @Test
    void testCreateProject_WithChineseCharactersInFullName() {
        // Given - 测试包含中文字符的情况
        mockSubSystem.setFullNameCn("包含中文字符的应用名称");
        mockSubSystem.setSubCode("valid-english-code");
        
        Long subSystemId = 1L;
        String groupId = "10";
        
       // when(subsystemRepository.getById(subSystemId)).thenReturn(mockSubSystem);
        when(systemComponentService.getComponentKeyBySystemId(mockSubSystem.getSystemId())).thenReturn(groupId);
        when(codeProjectRepository.createProject(any(ProjectRequest.class))).thenReturn(mockProjectResponse);

        // When
        ********************.createProject(subSystemId, new ProjectRequest(), false, false);

        // Then
        verify(codeProjectRepository).createProject(argThat(request -> {
            // 确保使用英文的 subCode，而不是中文的 fullNameCn
            assertEquals("valid-english-code", request.getName());
            assertEquals("valid-english-code", request.getPath());
            // fullNameCn 不应该被用作项目名称
            return !request.getName().equals("包含中文字符的应用名称");
        }));
    }
}
