package cn.harmonycloud.development.service.trinasolar.impl;

import cn.harmonycloud.development.execption.thgn.AppException;
import cn.harmonycloud.development.outbound.api.feign.IntegrationProvider;
import cn.harmonycloud.development.outbound.thgn.ApplicationProgramRepository;
import cn.harmonycloud.development.pojo.dto.thgn.ApplicationProgramDTO;
import cn.harmonycloud.issue.model.CommonResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ApplicationProgramServiceImpl 测试类
 * 主要测试应用程序英文名称的GitLab命名规范验证
 */
@ExtendWith(MockitoExtension.class)
class ApplicationProgramServiceImplTest {

    @Mock
    private ApplicationProgramRepository applicationProgramRepository;

    @Mock
    private IntegrationProvider integrationProvider;

    @InjectMocks
    private ApplicationProgramServiceImpl applicationProgramService;

    private ApplicationProgramDTO validProgramDTO;
    private ApplicationProgramDTO invalidProgramDTO;

    @BeforeEach
    void setUp() {
        // 创建有效的应用程序DTO
        validProgramDTO = new ApplicationProgramDTO();
        validProgramDTO.setApplicationId(1L);
        validProgramDTO.setProgramNameCn("测试应用");
        validProgramDTO.setProgramNameEn("test-app-123"); // 符合GitLab命名规范
        validProgramDTO.setProgramDescCn("测试应用描述");

        // 创建无效的应用程序DTO
        invalidProgramDTO = new ApplicationProgramDTO();
        invalidProgramDTO.setApplicationId(1L);
        invalidProgramDTO.setProgramNameCn("测试应用");
        invalidProgramDTO.setProgramNameEn("测试应用-invalid"); // 包含中文字符，不符合GitLab命名规范
        invalidProgramDTO.setProgramDescCn("测试应用描述");
    }

    @Test
    void testCheckCreate_ValidProgramNameEn_ShouldPass() {
        // Given
        Long applicationId = 1L;
        
        // Mock 系统初始化检查
        CommonResult<Boolean> initResult = new CommonResult<>();
        initResult.setCode(200);
        initResult.setData(true);
        when(integrationProvider.initCheck(applicationId)).thenReturn(initResult);
        
        // Mock 数据库查询返回空（没有重复名称）
        when(applicationProgramRepository.lambdaQuery()).thenReturn(mock(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper.class));
        when(applicationProgramRepository.lambdaQuery().eq(any(), any()).eq(any(), any()).oneOpt())
                .thenReturn(Optional.empty());

        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method checkCreateMethod = ApplicationProgramServiceImpl.class
                    .getDeclaredMethod("checkCreate", Long.class, ApplicationProgramDTO.class, Boolean.class);
            checkCreateMethod.setAccessible(true);
            checkCreateMethod.invoke(applicationProgramService, applicationId, validProgramDTO, true);
        });
    }

    @Test
    void testCheckCreate_InvalidProgramNameEn_ShouldThrowException() {
        // Given
        Long applicationId = 1L;
        
        // Mock 系统初始化检查
        CommonResult<Boolean> initResult = new CommonResult<>();
        initResult.setCode(200);
        initResult.setData(true);
        when(integrationProvider.initCheck(applicationId)).thenReturn(initResult);

        // When & Then - 应该抛出AppException
        AppException exception = assertThrows(AppException.class, () -> {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method checkCreateMethod = ApplicationProgramServiceImpl.class
                    .getDeclaredMethod("checkCreate", Long.class, ApplicationProgramDTO.class, Boolean.class);
            checkCreateMethod.setAccessible(true);
            checkCreateMethod.invoke(applicationProgramService, applicationId, invalidProgramDTO, true);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    @Test
    void testCheckCreate_ProgramNameEnWithSpecialCharacters_ShouldThrowException() {
        // Given
        ApplicationProgramDTO specialCharDTO = new ApplicationProgramDTO();
        specialCharDTO.setApplicationId(1L);
        specialCharDTO.setProgramNameCn("测试应用");
        specialCharDTO.setProgramNameEn("test@app#123"); // 包含特殊字符
        specialCharDTO.setProgramDescCn("测试应用描述");
        
        Long applicationId = 1L;
        
        // Mock 系统初始化检查
        CommonResult<Boolean> initResult = new CommonResult<>();
        initResult.setCode(200);
        initResult.setData(true);
        when(integrationProvider.initCheck(applicationId)).thenReturn(initResult);

        // When & Then - 应该抛出AppException
        AppException exception = assertThrows(AppException.class, () -> {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method checkCreateMethod = ApplicationProgramServiceImpl.class
                    .getDeclaredMethod("checkCreate", Long.class, ApplicationProgramDTO.class, Boolean.class);
            checkCreateMethod.setAccessible(true);
            checkCreateMethod.invoke(applicationProgramService, applicationId, specialCharDTO, true);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    @Test
    void testCheckCreate_ProgramNameEnStartingWithHyphen_ShouldThrowException() {
        // Given
        ApplicationProgramDTO hyphenStartDTO = new ApplicationProgramDTO();
        hyphenStartDTO.setApplicationId(1L);
        hyphenStartDTO.setProgramNameCn("测试应用");
        hyphenStartDTO.setProgramNameEn("-test-app"); // 以连字符开头
        hyphenStartDTO.setProgramDescCn("测试应用描述");
        
        Long applicationId = 1L;
        
        // Mock 系统初始化检查
        CommonResult<Boolean> initResult = new CommonResult<>();
        initResult.setCode(200);
        initResult.setData(true);
        when(integrationProvider.initCheck(applicationId)).thenReturn(initResult);

        // When & Then - 应该抛出AppException
        AppException exception = assertThrows(AppException.class, () -> {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method checkCreateMethod = ApplicationProgramServiceImpl.class
                    .getDeclaredMethod("checkCreate", Long.class, ApplicationProgramDTO.class, Boolean.class);
            checkCreateMethod.setAccessible(true);
            checkCreateMethod.invoke(applicationProgramService, applicationId, hyphenStartDTO, true);
        });

        // 验证异常消息
        assertTrue(exception.getMessage().contains("应用程序英文名称格式错误"));
    }

    @Test
    void testCheckCreate_ValidProgramNameEnWithUnderscores_ShouldPass() {
        // Given
        ApplicationProgramDTO underscoreDTO = new ApplicationProgramDTO();
        underscoreDTO.setApplicationId(1L);
        underscoreDTO.setProgramNameCn("测试应用");
        underscoreDTO.setProgramNameEn("test_app_123"); // 包含下划线，应该是有效的
        underscoreDTO.setProgramDescCn("测试应用描述");
        
        Long applicationId = 1L;
        
        // Mock 系统初始化检查
        CommonResult<Boolean> initResult = new CommonResult<>();
        initResult.setCode(200);
        initResult.setData(true);
        when(integrationProvider.initCheck(applicationId)).thenReturn(initResult);
        
        // Mock 数据库查询返回空（没有重复名称）
        when(applicationProgramRepository.lambdaQuery()).thenReturn(mock(com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper.class));
        when(applicationProgramRepository.lambdaQuery().eq(any(), any()).eq(any(), any()).oneOpt())
                .thenReturn(Optional.empty());

        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> {
            // 使用反射调用私有方法进行测试
            java.lang.reflect.Method checkCreateMethod = ApplicationProgramServiceImpl.class
                    .getDeclaredMethod("checkCreate", Long.class, ApplicationProgramDTO.class, Boolean.class);
            checkCreateMethod.setAccessible(true);
            checkCreateMethod.invoke(applicationProgramService, applicationId, underscoreDTO, true);
        });
    }
}
